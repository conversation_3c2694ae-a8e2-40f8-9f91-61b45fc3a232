{"version": "2.0", "description": "Optimized configuration for improved TNGD backup system", "resource_management": {"max_threads": 6, "memory_threshold_mb": 2000, "cpu_threshold_percent": 80, "disk_threshold_percent": 85, "cleanup_interval_seconds": 300, "resource_check_interval_seconds": 60}, "query_settings": {"default_timeout_seconds": 1800, "large_table_timeout_seconds": 3600, "max_retries": 3, "retry_delay_seconds": 60, "connection_timeout_seconds": 30, "read_timeout_seconds": 300}, "streaming_config": {"enabled": true, "default_chunk_size": 50000, "max_chunk_size": 100000, "min_chunk_size": 10000, "streaming_threshold_rows": 100000, "memory_threshold_mb": 1500, "progress_report_interval": 5, "memory_check_interval": 3, "enable_adaptive_chunking": true, "chunk_size_adjustment_factor": 0.8, "temp_file_cleanup": true}, "large_tables": ["cef0.zscaler.nssweblog", "cloud.alibaba.log_service.events", "cloud.office365.management.exchange", "firewall.fortinet.traffic.forward", "cloud.office365.management.endpoint", "my.app.tngd.polardb", "netstat.zscaler.analyzer_zpa"], "table_specific_settings": {"cef0.zscaler.nssweblog": {"chunk_size": 25000, "timeout_seconds": 7200, "max_retries": 5, "memory_limit_mb": 1000}, "cloud.alibaba.log_service.events": {"chunk_size": 30000, "timeout_seconds": 7200, "max_retries": 5, "memory_limit_mb": 1200}, "firewall.fortinet.traffic.forward": {"chunk_size": 20000, "timeout_seconds": 5400, "max_retries": 4, "memory_limit_mb": 800}, "cloud.office365.management.endpoint": {"chunk_size": 35000, "timeout_seconds": 5400, "max_retries": 4, "memory_limit_mb": 1000}}, "storage_settings": {"upload_timeout_seconds": 3600, "max_upload_retries": 3, "retry_delay_seconds": 30, "chunk_size_mb": 50, "memory_threshold_percent": 75, "connection_pool_size": 3, "verify_integrity": true, "compress_before_upload": true}, "backup_workflows": {"improved_daily": {"enabled": true, "max_duration_hours": 12, "checkpoint_interval_minutes": 30, "resource_monitoring": true, "auto_recovery": true, "validation": {"enabled": true, "post_backup_validation": true, "checksum_verification": true}, "cleanup": {"auto_cleanup": true, "retain_temp_files_on_error": true, "cleanup_interval_hours": 2}, "throttling": {"enabled": true, "cpu_threshold": 85, "memory_threshold": 80, "delay_seconds": 5}}}, "error_handling": {"max_consecutive_failures": 5, "failure_cooldown_minutes": 10, "auto_skip_problematic_tables": true, "detailed_error_logging": true, "error_notification_threshold": 3}, "monitoring": {"enabled": true, "log_level": "INFO", "metrics_collection": true, "health_check_interval_seconds": 300, "alert_thresholds": {"cpu_warning": 80, "cpu_critical": 95, "memory_warning": 75, "memory_critical": 90, "thread_warning": 100, "thread_critical": 200}}, "recovery": {"checkpoint_enabled": true, "checkpoint_interval_minutes": 15, "auto_resume": true, "max_resume_attempts": 3, "resume_delay_minutes": 5}, "performance_optimizations": {"connection_pooling": true, "query_caching": false, "parallel_uploads": true, "memory_mapping": false, "compression_level": 6, "buffer_size_kb": 64}, "logging": {"level": "INFO", "max_file_size_mb": 100, "backup_count": 5, "format": "%(asctime)s - %(levelname)s - %(message)s", "separate_error_log": true, "log_rotation": true}}