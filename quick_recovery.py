#!/usr/bin/env python3
"""
Quick Recovery Script for TNGD Backup System

This script provides immediate fixes for the identified issues:
- Thread management problems
- Memory exhaustion
- Resource cleanup
- Configuration optimization

Usage:
    python quick_recovery.py --fix-all
    python quick_recovery.py --cleanup-only
    python quick_recovery.py --config-only
"""

import os
import sys
import json
import shutil
import logging
import subprocess
from datetime import datetime
from pathlib import Path


class QuickRecovery:
    """Quick recovery and fix implementation."""
    
    def __init__(self):
        """Initialize quick recovery."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Paths
        self.config_dir = Path("config")
        self.logs_dir = Path("logs")
        self.temp_dir = Path("temp")
        self.checkpoints_dir = Path("checkpoints")
        
        self.logger.info("QuickRecovery initialized")
    
    def setup_logging(self):
        """Setup logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
    
    def apply_emergency_config(self):
        """Apply emergency configuration with conservative settings."""
        self.logger.info("Applying emergency configuration...")
        
        emergency_config = {
            "version": "2.0-emergency",
            "description": "Emergency configuration for backup recovery",
            
            "resource_management": {
                "max_threads": 4,  # Very conservative
                "memory_threshold_mb": 1500,  # Lower threshold
                "cpu_threshold_percent": 75,
                "cleanup_interval_seconds": 180,  # More frequent cleanup
                "resource_check_interval_seconds": 30
            },
            
            "query_settings": {
                "default_timeout_seconds": 1200,  # 20 minutes
                "large_table_timeout_seconds": 2400,  # 40 minutes
                "max_retries": 2,  # Fewer retries to avoid hanging
                "retry_delay_seconds": 30,  # Shorter delay
                "connection_timeout_seconds": 20,
                "read_timeout_seconds": 180
            },
            
            "streaming_config": {
                "enabled": True,
                "default_chunk_size": 20000,  # Very small chunks
                "max_chunk_size": 50000,
                "min_chunk_size": 5000,
                "streaming_threshold_rows": 50000,  # Lower threshold
                "memory_threshold_mb": 1000,  # Conservative memory limit
                "progress_report_interval": 3,
                "memory_check_interval": 2,  # More frequent checks
                "enable_adaptive_chunking": True,
                "chunk_size_adjustment_factor": 0.7,  # More aggressive reduction
                "temp_file_cleanup": True
            },
            
            "large_tables": [
                "cef0.zscaler.nssweblog",
                "cloud.alibaba.log_service.events",
                "firewall.fortinet.traffic.forward",
                "cloud.office365.management.endpoint",
                "my.app.tngd.polardb"
            ],
            
            "table_specific_settings": {
                "cef0.zscaler.nssweblog": {
                    "chunk_size": 10000,  # Very small for problematic table
                    "timeout_seconds": 3600,
                    "max_retries": 3,
                    "memory_limit_mb": 500
                },
                "firewall.fortinet.traffic.forward": {
                    "chunk_size": 15000,
                    "timeout_seconds": 2400,
                    "max_retries": 3,
                    "memory_limit_mb": 600
                },
                "cloud.office365.management.endpoint": {
                    "chunk_size": 15000,
                    "timeout_seconds": 2400,
                    "max_retries": 3,
                    "memory_limit_mb": 600
                }
            },
            
            "error_handling": {
                "max_consecutive_failures": 3,  # Fail fast
                "failure_cooldown_minutes": 5,
                "auto_skip_problematic_tables": True,
                "detailed_error_logging": True
            },
            
            "monitoring": {
                "enabled": True,
                "health_check_interval_seconds": 60,  # Frequent monitoring
                "alert_thresholds": {
                    "cpu_warning": 70,
                    "cpu_critical": 85,
                    "memory_warning": 65,
                    "memory_critical": 80,
                    "thread_warning": 50,
                    "thread_critical": 100
                }
            }
        }
        
        # Backup existing config
        config_file = self.config_dir / "config.json"
        if config_file.exists():
            backup_file = self.config_dir / f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            shutil.copy2(config_file, backup_file)
            self.logger.info(f"Backed up existing config to: {backup_file}")
        
        # Write emergency config
        self.config_dir.mkdir(exist_ok=True)
        with open(config_file, 'w') as f:
            json.dump(emergency_config, f, indent=2)
        
        self.logger.info("✅ Emergency configuration applied")
        return True
    
    def cleanup_system_resources(self):
        """Perform aggressive system cleanup."""
        self.logger.info("Performing system resource cleanup...")
        
        cleanup_results = {
            "temp_files_cleaned": 0,
            "memory_freed": False,
            "processes_cleaned": 0
        }
        
        try:
            # Clean temp directory
            if self.temp_dir.exists():
                for item in self.temp_dir.iterdir():
                    try:
                        if item.is_file():
                            item.unlink()
                            cleanup_results["temp_files_cleaned"] += 1
                        elif item.is_dir():
                            shutil.rmtree(item)
                            cleanup_results["temp_files_cleaned"] += 1
                    except Exception as e:
                        self.logger.warning(f"Could not remove {item}: {e}")
            
            # Force Python garbage collection
            import gc
            gc.collect()
            cleanup_results["memory_freed"] = True
            
            # Kill any hanging Python processes (be careful!)
            try:
                import psutil
                current_pid = os.getpid()
                
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if (proc.info['name'] and 'python' in proc.info['name'].lower() and
                            proc.info['cmdline'] and any('tngd_backup' in cmd for cmd in proc.info['cmdline']) and
                            proc.info['pid'] != current_pid):
                            
                            self.logger.warning(f"Found hanging backup process: PID {proc.info['pid']}")
                            # Uncomment the next line if you want to kill hanging processes
                            # proc.terminate()
                            # cleanup_results["processes_cleaned"] += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            
            except ImportError:
                self.logger.info("psutil not available for process cleanup")
        
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")
        
        self.logger.info(f"✅ Cleanup completed: {cleanup_results}")
        return cleanup_results
    
    def check_prerequisites(self):
        """Check if system is ready for recovery."""
        self.logger.info("Checking system prerequisites...")
        
        checks = {
            "config_dir": self.config_dir.exists(),
            "logs_dir": self.logs_dir.exists(),
            "python_version": sys.version_info >= (3, 7),
            "disk_space": True,  # Will check below
            "memory_available": True  # Will check below
        }
        
        try:
            import psutil
            
            # Check disk space (need at least 5GB free)
            disk_usage = psutil.disk_usage('.')
            free_gb = disk_usage.free / (1024**3)
            checks["disk_space"] = free_gb >= 5.0
            
            # Check available memory (need at least 1GB)
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            checks["memory_available"] = available_gb >= 1.0
            
            self.logger.info(f"Disk space: {free_gb:.1f}GB free")
            self.logger.info(f"Memory available: {available_gb:.1f}GB")
        
        except ImportError:
            self.logger.warning("psutil not available for resource checks")
        
        all_good = all(checks.values())
        if all_good:
            self.logger.info("✅ All prerequisites met")
        else:
            failed_checks = [k for k, v in checks.items() if not v]
            self.logger.error(f"❌ Failed checks: {failed_checks}")
        
        return all_good, checks
    
    def run_recovery(self, fix_all=False, cleanup_only=False, config_only=False):
        """Run the recovery process."""
        self.logger.info("=" * 50)
        self.logger.info("TNGD BACKUP SYSTEM - QUICK RECOVERY")
        self.logger.info("=" * 50)
        
        # Check prerequisites
        ready, checks = self.check_prerequisites()
        if not ready:
            self.logger.error("System not ready for recovery. Please address failed checks.")
            return False
        
        success = True
        
        try:
            if cleanup_only or fix_all:
                self.cleanup_system_resources()
            
            if config_only or fix_all:
                self.apply_emergency_config()
            
            if fix_all:
                self.logger.info("\n" + "=" * 50)
                self.logger.info("RECOVERY COMPLETED SUCCESSFULLY")
                self.logger.info("=" * 50)
                self.logger.info("\nNext steps:")
                self.logger.info("1. Run: python tngd_backup_improved.py 2025-03-26 2025-03-31")
                self.logger.info("2. Monitor: python tools/resource_monitor.py --monitor")
                self.logger.info("3. Check progress in logs/")
                self.logger.info("\nThe system is now configured with conservative settings.")
                self.logger.info("Monitor the backup closely and adjust settings as needed.")
        
        except Exception as e:
            self.logger.error(f"Recovery failed: {e}")
            success = False
        
        return success


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Quick Recovery for TNGD Backup System")
    parser.add_argument("--fix-all", action="store_true", help="Apply all fixes (recommended)")
    parser.add_argument("--cleanup-only", action="store_true", help="Only perform cleanup")
    parser.add_argument("--config-only", action="store_true", help="Only apply emergency config")
    
    args = parser.parse_args()
    
    if not any([args.fix_all, args.cleanup_only, args.config_only]):
        print("Please specify an action:")
        print("  --fix-all      Apply all fixes (recommended)")
        print("  --cleanup-only Only perform cleanup")
        print("  --config-only  Only apply emergency config")
        return
    
    recovery = QuickRecovery()
    success = recovery.run_recovery(
        fix_all=args.fix_all,
        cleanup_only=args.cleanup_only,
        config_only=args.config_only
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
