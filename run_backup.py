#!/usr/bin/env python3
"""
TNGD Backup System - Easy Run Script

This script provides an easy way to run the improved backup system
with proper error handling and monitoring.

Usage:
    python run_backup.py                    # Today's data
    python run_backup.py 2025-03-26         # Single date
    python run_backup.py 2025-03-26 2025-03-31  # Date range
"""

import os
import sys
import subprocess
import time
from datetime import datetime
from pathlib import Path


def check_prerequisites():
    """Check if all required files exist."""
    required_files = [
        'tngd_backup_improved.py',
        'core/thread_manager.py',
        'core/compression_service.py',
        'core/storage_manager.py',
        'config/config.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ All required files found")
    return True


def start_monitoring():
    """Start resource monitoring in background."""
    try:
        print("🔍 Starting resource monitoring...")
        monitor_process = subprocess.Popen(
            [sys.executable, 'tools/resource_monitor.py', '--monitor', '--interval', '30'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        print(f"✅ Resource monitor started (PID: {monitor_process.pid})")
        return monitor_process
    except Exception as e:
        print(f"⚠️ Could not start resource monitor: {e}")
        return None


def run_backup(dates_args):
    """Run the improved backup system."""
    try:
        print("🚀 Starting improved backup system...")
        
        # Build command
        cmd = [sys.executable, 'tngd_backup_improved.py'] + dates_args
        
        print(f"Command: {' '.join(cmd)}")
        print("=" * 60)
        
        # Run backup
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Stream output in real-time
        for line in iter(process.stdout.readline, ''):
            print(line.rstrip())
        
        # Wait for completion
        return_code = process.wait()
        
        print("=" * 60)
        if return_code == 0:
            print("✅ Backup completed successfully!")
        else:
            print("❌ Backup failed!")
        
        return return_code == 0
    
    except KeyboardInterrupt:
        print("\n⚠️ Backup interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Error running backup: {e}")
        return False


def show_usage():
    """Show usage instructions."""
    print("TNGD Backup System - Improved Version")
    print("=" * 40)
    print()
    print("Usage:")
    print("  python run_backup.py                    # Today's data")
    print("  python run_backup.py 2025-03-26         # Single date")
    print("  python run_backup.py 2025-03-26 2025-03-31  # Date range")
    print()
    print("Examples:")
    print("  python run_backup.py                    # Backup today")
    print("  python run_backup.py 2025-03-26         # Backup March 26, 2025")
    print("  python run_backup.py 2025-03-26 2025-03-31  # Backup March 26-31, 2025")
    print()
    print("Features:")
    print("  ✅ Thread management (max 4 threads)")
    print("  ✅ Memory optimization (1500MB limit)")
    print("  ✅ Automatic retry and recovery")
    print("  ✅ Real-time progress monitoring")
    print("  ✅ Checkpoint system for resume")
    print("  ✅ Resource monitoring")
    print()


def main():
    """Main function."""
    print("🔧 TNGD Backup System - Improved Version")
    print("=" * 50)
    
    # Show usage if requested
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_usage()
        return
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed!")
        print("Please ensure all required files are present.")
        return
    
    # Start monitoring
    monitor_process = start_monitoring()
    
    try:
        # Run backup
        dates_args = sys.argv[1:] if len(sys.argv) > 1 else []
        success = run_backup(dates_args)
        
        if success:
            print("\n🎉 Backup operation completed successfully!")
        else:
            print("\n💥 Backup operation failed!")
        
    finally:
        # Stop monitoring
        if monitor_process:
            try:
                monitor_process.terminate()
                monitor_process.wait(timeout=5)
                print("🔍 Resource monitor stopped")
            except:
                try:
                    monitor_process.kill()
                except:
                    pass


if __name__ == "__main__":
    main()
