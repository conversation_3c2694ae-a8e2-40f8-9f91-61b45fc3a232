#!/usr/bin/env python3
"""
Simple Backup Progress Monitor

This script monitors the progress of the improved backup system
and provides real-time status updates.
"""

import os
import time
import re
from datetime import datetime
from pathlib import Path


def get_latest_backup_log():
    """Find the latest backup log file."""
    logs_dir = Path("logs")
    if not logs_dir.exists():
        return None
    
    # Look for improved backup logs
    log_files = list(logs_dir.glob("tngd_backup_improved_*.log"))
    if not log_files:
        return None
    
    # Return the most recent one
    return max(log_files, key=lambda f: f.stat().st_mtime)


def parse_backup_progress(log_file):
    """Parse backup progress from log file."""
    if not log_file or not log_file.exists():
        return None
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract key information
        progress = {
            'start_time': None,
            'current_date': None,
            'current_table': None,
            'completed_tables': 0,
            'failed_tables': 0,
            'total_dates': 0,
            'current_date_num': 0,
            'total_rows': 0,
            'last_activity': None,
            'status': 'running'
        }
        
        # Find start time
        start_match = re.search(r'BACKUP_START.*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', content)
        if start_match:
            progress['start_time'] = start_match.group(1)
        
        # Find total dates
        dates_match = re.search(r'Dates to process: (\d+)', content)
        if dates_match:
            progress['total_dates'] = int(dates_match.group(1))
        
        # Find current date
        date_match = re.search(r'Processing date (\d+)/\d+: (\d{4}-\d{2}-\d{2})', content)
        if date_match:
            progress['current_date_num'] = int(date_match.group(1))
            progress['current_date'] = date_match.group(2)
        
        # Find current table
        table_match = re.search(r'TABLE_START.*Starting backup: ([^\s]+)', content)
        if table_match:
            progress['current_table'] = table_match.group(1)
        
        # Count completed tables
        completed_matches = re.findall(r'TABLE_SUCCESS.*backup completed successfully', content)
        progress['completed_tables'] = len(completed_matches)
        
        # Count failed tables
        failed_matches = re.findall(r'TABLE_FAILED.*backup failed', content)
        progress['failed_tables'] = len(failed_matches)
        
        # Count total rows
        row_matches = re.findall(r'Query completed: (\d+) rows', content)
        progress['total_rows'] = sum(int(match) for match in row_matches)
        
        # Find last activity
        lines = content.strip().split('\n')
        if lines:
            last_line = lines[-1]
            time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', last_line)
            if time_match:
                progress['last_activity'] = time_match.group(1)
        
        # Check if completed
        if 'BACKUP_SUMMARY' in content and 'IMPROVED BACKUP COMPLETED' in content:
            progress['status'] = 'completed'
        elif 'BACKUP_FAILED' in content:
            progress['status'] = 'failed'
        
        return progress
    
    except Exception as e:
        print(f"Error parsing log: {e}")
        return None


def format_duration(start_time_str):
    """Calculate and format duration."""
    if not start_time_str:
        return "Unknown"
    
    try:
        start_time = datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
        duration = datetime.now() - start_time
        
        hours = duration.seconds // 3600
        minutes = (duration.seconds % 3600) // 60
        
        if duration.days > 0:
            return f"{duration.days}d {hours}h {minutes}m"
        elif hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
    
    except:
        return "Unknown"


def display_progress(progress):
    """Display formatted progress information."""
    if not progress:
        print("❌ No backup progress found")
        return
    
    print("=" * 60)
    print("🔄 TNGD BACKUP PROGRESS MONITOR")
    print("=" * 60)
    
    # Status
    status_emoji = {
        'running': '🔄',
        'completed': '✅',
        'failed': '❌'
    }
    
    print(f"Status: {status_emoji.get(progress['status'], '❓')} {progress['status'].upper()}")
    
    # Duration
    duration = format_duration(progress['start_time'])
    print(f"Duration: {duration}")
    
    # Date progress
    if progress['total_dates'] > 0:
        date_percent = (progress['current_date_num'] / progress['total_dates']) * 100
        print(f"Date Progress: {progress['current_date_num']}/{progress['total_dates']} ({date_percent:.1f}%)")
        print(f"Current Date: {progress['current_date']}")
    
    # Table progress
    total_tables = progress['completed_tables'] + progress['failed_tables']
    print(f"Tables Completed: {progress['completed_tables']}")
    print(f"Tables Failed: {progress['failed_tables']}")
    print(f"Total Rows Processed: {progress['total_rows']:,}")
    
    # Current activity
    if progress['current_table']:
        print(f"Current Table: {progress['current_table']}")
    
    if progress['last_activity']:
        print(f"Last Activity: {progress['last_activity']}")
    
    print("=" * 60)


def main():
    """Main monitoring loop."""
    print("🔍 Starting backup progress monitor...")
    print("Press Ctrl+C to stop monitoring\n")
    
    try:
        while True:
            # Clear screen (works on most terminals)
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Get latest log
            log_file = get_latest_backup_log()
            
            if not log_file:
                print("❌ No backup log files found")
                print("Make sure the backup is running: python tngd_backup_improved.py")
            else:
                # Parse and display progress
                progress = parse_backup_progress(log_file)
                display_progress(progress)
                
                print(f"\nLog File: {log_file}")
                print(f"Updated: {datetime.now().strftime('%H:%M:%S')}")
                
                # Check if backup completed
                if progress and progress['status'] in ['completed', 'failed']:
                    print(f"\n🎯 Backup {progress['status']}! Monitor stopping.")
                    break
            
            print("\nRefreshing in 30 seconds...")
            time.sleep(30)
    
    except KeyboardInterrupt:
        print("\n\n👋 Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Monitor error: {e}")


if __name__ == "__main__":
    main()
