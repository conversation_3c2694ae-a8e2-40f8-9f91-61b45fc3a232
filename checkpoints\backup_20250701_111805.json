{"backup_id": "backup_20250701_111805", "start_time": "2025-07-01T11:18:05.662404", "dates": ["2025-06-30"], "tables": ["my.app.tngd.actiontraillinux", "my.app.tngd.waf", "cloud.office365.management.exchange", "cloud.alibaba.log_service.events", "cef0.zscaler.nssweblog", "cloud.office365.management.airinvestigation", "edr.crowdstrike.falconstreaming.agents", "box.stat.unix.diskstat", "nac.aruba.clearpass.session", "my.app.tngd.ciscoswitch", "my.app.tngd.actiontrailwindows", "my.app.tngd.adminportal", "my.app.tngd.cfw", "my.app.tngd.cyberark", "my.app.tngd.ezeelogin", "my.app.tngd.h3ccoreswitch", "my.app.tngd.h3cswitch", "my.app.tngd.h3cwirelessctrl", "my.app.tngd.keeper", "my.app.tngd.polardb", "my.app.tngd.rds", "my.app.tngd.sas", "firewall.fortinet.event.connector", "firewall.fortinet.event.ha", "firewall.fortinet.event.sdwan", "firewall.fortinet.event.system", "firewall.fortinet.event.user", "firewall.fortinet.event.vpn", "firewall.fortinet.traffic.forward", "firewall.fortinet.utm.ssl", "firewall.fortinet.utm.webfilter", "cloud.office365.management.azureactivedirectory", "cloud.office365.management.complianceposturemanagement", "cloud.office365.management.copilot", "cloud.office365.management.endpoint", "cloud.office365.management.microsoftteams", "cloud.office365.management.microsofttodo", "cloud.office365.management.onedrive", "cloud.office365.management.planner", "cloud.office365.management.powerbi", "cloud.office365.management.powerplatform", "cloud.office365.management.publicendpoint", "cloud.office365.management.quarantine", "cloud.office365.management.securitycompliancecenter", "cloud.office365.management.sharepoint", "cloud.office365.management.threatintelligence", "cloud.office365.management.workplaceanalytics", "cloud.office365.management.yammer", "edr.crowdstrike.falconstreaming.alert", "edr.crowdstrike.falconstreaming.auth_activity", "edr.crowdstrike.falconstreaming.behaviors", "edr.crowdstrike.falconstreaming.detection_summary", "edr.crowdstrike.falconstreaming.epp_detection_summary", "edr.crowdstrike.falconstreaming.other", "edr.crowdstrike.falconstreaming.user_activity_all", "edr.crowdstrike.falconstreaming.user_activity_detections", "edr.crowdstrike.falconstreaming.user_activity_groups", "edr.crowdstrike.falconstreaming.user_activity_prevention_policy", "app.lark.audit.event", "firewall.fortinet.traffic.local", "netstat.zscaler.analyzer_zpa", "cloud.office365.management.microsoftflow", "cloud.office365.management.microsoftforms"], "completed_tables": {}, "failed_tables": {}, "current_date": "2025-06-30", "current_table": null}