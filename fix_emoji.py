#!/usr/bin/env python3
"""
Fix emoji characters in backup script for Windows compatibility.
"""

import re

def fix_emoji_in_file(filename):
    """Remove emoji characters from file."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace emoji characters with text equivalents
        replacements = {
            '✅': '[OK]',
            '❌': '[ERROR]',
            '⚠️': '[WARNING]',
            '📊': '[DATA]',
            '📭': '[NO_DATA]',
            '⏱️': '[TIME]'
        }
        
        for emoji, replacement in replacements.items():
            content = content.replace(emoji, replacement)
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Fixed emoji characters in {filename}")
        return True
    
    except Exception as e:
        print(f"Error fixing {filename}: {e}")
        return False

if __name__ == "__main__":
    files_to_fix = [
        'tngd_backup_improved.py',
        'test_improved_backup.py'
    ]
    
    for filename in files_to_fix:
        fix_emoji_in_file(filename)
