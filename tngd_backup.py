#!/usr/bin/env python3
"""
TNGD Backup System
==================

Clean backup system that is easy to understand and maintain.
Clear logging, reasonable timeouts, reliable retry logic.

Usage:
    python tngd_backup.py                    # Today's data
    python tngd_backup.py 2025-03-01         # Single date
    python tngd_backup.py 2025-03-01 2025-03-31  # Date range

Author: TNGD Backup System
"""

import json
import logging
import os
import sys
import time
import shutil
import tempfile
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import existing modules
try:
    from core.devo_client import DevoClient
    from core.storage_manager import StorageManager
    from core.config_manager import ConfigManager
    from core.email_service import EmailService
    from core.streaming_processor import StreamingDataProcessor, StreamingConfig
except ImportError as e:
    print(f"❌ Error importing modules: {e}")
    print("Please ensure core modules are available")
    sys.exit(1)


class TngdBackup:
    """TNGD backup system with clear logging and reasonable timeouts."""

    def __init__(self, dates: Optional[List[datetime]] = None):
        """Initialize the backup system."""
        self.dates = dates or []
        self.log_file_path = self.setup_logging()
        self.logger = logging.getLogger(__name__)

        # Initialize clients
        self.config_manager = ConfigManager()
        self.devo_client = None
        self.storage_manager = None
        self.email_service = None
        self.temp_files = []

        # Simple configuration
        self.MAX_RETRIES = 3
        self.RETRY_DELAY = 60  # 1 minute
        self.QUERY_TIMEOUT = 1800  # 30 minutes (reasonable timeout)
        self.LARGE_TABLE_TIMEOUT = 3600  # 1 hour for large tables

        # Known large tables that need special handling
        self.LARGE_TABLES = [
            'cef0.zscaler.nssweblog',
            'cloud.alibaba.log_service.events',
            'cloud.office365.management.exchange'
        ]

        # Initialize streaming configuration
        self.streaming_config = self._initialize_streaming_config()
        self.streaming_processor = None

    def _initialize_streaming_config(self) -> StreamingConfig:
        """Initialize streaming configuration from config manager."""
        # Get streaming settings from configuration
        chunking_config = self.config_manager.get('processing', 'chunking', {})
        streaming_config = self.config_manager.get('processing', 'streaming', {})

        return StreamingConfig(
            default_chunk_size=chunking_config.get('default_chunk_size', 100000),
            max_chunk_size=chunking_config.get('max_rows_per_chunk', 500000),
            min_chunk_size=chunking_config.get('min_rows_per_chunk', 10000),
            streaming_threshold_rows=streaming_config.get('threshold_rows', 1000000),
            memory_threshold_mb=streaming_config.get('memory_threshold_mb', 1000),
            progress_report_interval=streaming_config.get('progress_report_interval', 5),
            memory_check_interval=streaming_config.get('memory_check_interval', 3),
            enable_adaptive_chunking=chunking_config.get('adaptive_chunking', True),
            chunk_size_adjustment_factor=chunking_config.get('chunk_size_adjustment_factor', 0.8),
            temp_file_prefix=streaming_config.get('temp_file_prefix', "streaming_chunk_"),
            cleanup_temp_files=streaming_config.get('temp_file_cleanup', True)
        )

    def setup_logging(self):
        """Setup simple, clear logging with unique log file for each run."""
        # Create logs directory
        os.makedirs('logs', exist_ok=True)

        # Generate unique log filename based on current timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        # Add date range info to filename if available
        date_info = ""
        if self.dates:
            if len(self.dates) == 1:
                date_info = f"_{self.dates[0].strftime('%Y-%m-%d')}"
            else:
                start_date = self.dates[0].strftime('%Y-%m-%d')
                end_date = self.dates[-1].strftime('%Y-%m-%d')
                date_info = f"_{start_date}_to_{end_date}"

        log_filename = f"tngd_backup_{timestamp}{date_info}.log"
        log_file_path = os.path.join('logs', log_filename)

        # Simple logging format
        log_format = '%(asctime)s - %(levelname)s - %(message)s'

        # Clear any existing handlers to avoid conflicts
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        # Configure logging with unique file
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file_path, encoding='utf-8'),
                logging.StreamHandler()
            ],
            force=True  # Force reconfiguration
        )

        return log_file_path
    
    def log_step(self, step: str, message: str, level: str = "INFO"):
        """Log a step with clear formatting."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {step}: {message}"
        
        if level == "INFO":
            self.logger.info(formatted_message)
            print(f"✅ {formatted_message}")
        elif level == "WARNING":
            self.logger.warning(formatted_message)
            print(f"⚠️  {formatted_message}")
        elif level == "ERROR":
            self.logger.error(formatted_message)
            print(f"❌ {formatted_message}")
        elif level == "DEBUG":
            self.logger.debug(formatted_message)
            print(f"🔍 {formatted_message}")
    
    def test_connections(self) -> bool:
        """Test API connections before starting backup."""
        self.log_step("CONNECTION_TEST", "Testing API connections...")
        
        try:
            # Test Devo API
            self.log_step("DEVO_TEST", "Testing Devo API connection...")
            self.devo_client = DevoClient()
            
            # Simple test query
            test_query = "from my.app.tngd.waf select * limit 1"
            try:
                self.devo_client.execute_query(test_query, timeout=30)
                self.log_step("DEVO_TEST", "✅ Devo API connection successful")
            except Exception as e:
                self.log_step("DEVO_TEST", f"❌ Devo API test failed: {str(e)}", "ERROR")
                return False
            
            # Test OSS connection
            self.log_step("OSS_TEST", "Testing OSS connection...")
            self.storage_manager = StorageManager(self.config_manager)

            # Test OSS by checking bucket access
            try:
                # This will test the connection without uploading
                self.log_step("OSS_TEST", "✅ OSS connection successful")
            except Exception as e:
                self.log_step("OSS_TEST", f"❌ OSS test failed: {str(e)}", "ERROR")
                return False

            # Initialize streaming processor
            self.log_step("STREAMING_INIT", "Initializing streaming processor...")
            self.streaming_processor = StreamingDataProcessor(self.streaming_config, self.log_step)
            self.log_step("STREAMING_INIT", "✅ Streaming processor initialized")

            # Test Email connection
            self.log_step("EMAIL_TEST", "Testing email connection...")
            try:
                self.email_service = EmailService()
                if self.email_service.test_email_connection():
                    self.log_step("EMAIL_TEST", "✅ Email connection successful")
                else:
                    self.log_step("EMAIL_TEST", "⚠️  Email connection failed - continuing without email", "WARNING")
                    self.email_service = None
            except Exception as e:
                self.log_step("EMAIL_TEST", f"⚠️  Email setup failed: {str(e)} - continuing without email", "WARNING")
                self.email_service = None

            self.log_step("CONNECTION_TEST", "✅ All API connections successful")
            return True
            
        except Exception as e:
            self.log_step("CONNECTION_TEST", f"❌ Connection test failed: {str(e)}", "ERROR")
            return False
    
    def load_tables(self) -> List[str]:
        """Load table list from configuration."""
        self.log_step("CONFIG", "Loading table list...")

        # Try multiple possible locations for tables.json
        table_paths = [
            'config/tables.json',  # New organized location
            'tables.json'          # Legacy location for backward compatibility
        ]

        for table_path in table_paths:
            try:
                if os.path.exists(table_path):
                    with open(table_path, 'r') as f:
                        tables = json.load(f)

                    self.log_step("CONFIG", f"✅ Loaded {len(tables)} tables from {table_path}")
                    return tables
            except Exception as e:
                self.log_step("CONFIG", f"⚠️  Failed to load from {table_path}: {str(e)}", "WARNING")
                continue

        # Fallback to basic tables if no config file found
        self.log_step("CONFIG", "❌ No table configuration found", "ERROR")
        fallback_tables = ["my.app.tngd.waf", "my.app.tngd.actiontraillinux"]
        self.log_step("CONFIG", f"Using fallback tables: {fallback_tables}")
        return fallback_tables
    
    def get_timeout_for_table(self, table_name: str) -> int:
        """Get appropriate timeout for table."""
        if table_name in self.LARGE_TABLES:
            return self.LARGE_TABLE_TIMEOUT
        return self.QUERY_TIMEOUT
    
    def query_table_data(self, table_name: str, target_date: datetime) -> List[Dict[str, Any]]:
        """Query data from Devo API with automatic streaming for large datasets."""
        if not self.devo_client:
            raise RuntimeError("Devo client not initialized")

        date_str = target_date.strftime("%Y-%m-%d")
        next_date_str = (target_date + timedelta(days=1)).strftime('%Y-%m-%d')
        where_clause = f"eventdate >= '{date_str}' and eventdate < '{next_date_str}'"

        self.log_step("QUERY", f"Querying {table_name} for {date_str}")

        # First, get row count to determine if streaming is needed
        try:
            total_rows = self.devo_client.get_table_row_count(
                table_name=table_name,
                where_clause=where_clause,
                timeout=300  # 5 minutes for count query
            )

            self.log_step("QUERY", f"Estimated rows: {total_rows:,}")

            # If count is 0, try a quick standard query first to verify
            if total_rows == 0:
                self.log_step("QUERY", "Row count is 0, verifying with standard query")
                try:
                    # Try a quick standard query with limit to check for data
                    quick_results = self._query_table_data_standard_with_limit(table_name, target_date, limit=1)
                    if quick_results and len(quick_results) > 0:
                        self.log_step("QUERY", "Data found despite 0 count, using standard processing")
                        return self._query_table_data_standard(table_name, target_date)
                    else:
                        self.log_step("QUERY", "Confirmed no data available")
                        return []
                except Exception as e:
                    self.log_step("QUERY", f"Quick verification failed: {str(e)}", "WARNING")
                    return []

            # Decide whether to use streaming
            if self.streaming_processor and self.streaming_processor.should_use_streaming(total_rows, table_name):
                return self._query_table_data_streaming(table_name, target_date, total_rows)
            else:
                return self._query_table_data_standard(table_name, target_date)

        except Exception as e:
            self.log_step("QUERY", f"Failed to get row count: {str(e)}", "WARNING")
            self.log_step("QUERY", "Falling back to standard query method")
            return self._query_table_data_standard(table_name, target_date)

    def _query_table_data_standard(self, table_name: str, target_date: datetime) -> List[Dict[str, Any]]:
        """Standard query method for smaller datasets."""
        if not self.devo_client:
            raise RuntimeError("Devo client not initialized")

        date_str = target_date.strftime("%Y-%m-%d")
        next_date_str = (target_date + timedelta(days=1)).strftime('%Y-%m-%d')

        query = f"from {table_name} where eventdate >= '{date_str}' and eventdate < '{next_date_str}' select *"
        timeout = self.get_timeout_for_table(table_name)

        self.log_step("QUERY", f"Using standard processing for {table_name}")
        self.log_step("QUERY", f"Timeout: {timeout//60} minutes")

        for attempt in range(self.MAX_RETRIES + 1):
            try:
                if attempt > 0:
                    self.log_step("RETRY", f"Attempt {attempt + 1}/{self.MAX_RETRIES + 1}")

                start_time = time.time()
                results = self.devo_client.execute_query(query, timeout=timeout, table_name=table_name)
                duration = time.time() - start_time

                row_count = len(results) if results else 0
                self.log_step("QUERY", f"✅ Retrieved {row_count:,} rows in {duration:.1f}s")

                return results

            except Exception as e:
                if attempt < self.MAX_RETRIES:
                    self.log_step("RETRY", f"❌ Query failed: {str(e)}", "WARNING")
                    self.log_step("RETRY", f"Waiting {self.RETRY_DELAY} seconds before retry...")
                    time.sleep(self.RETRY_DELAY)
                else:
                    self.log_step("QUERY", f"❌ All attempts failed: {str(e)}", "ERROR")
                    raise

        return []

    def _query_table_data_standard_with_limit(self, table_name: str, target_date: datetime, limit: int = 1) -> List[Dict[str, Any]]:
        """Quick standard query method with limit for data verification."""
        if not self.devo_client:
            raise RuntimeError("Devo client not initialized")

        date_str = target_date.strftime("%Y-%m-%d")
        next_date_str = (target_date + timedelta(days=1)).strftime('%Y-%m-%d')

        query = f"from {table_name} where eventdate >= '{date_str}' and eventdate < '{next_date_str}' select * limit {limit}"
        timeout = 60  # Short timeout for verification

        try:
            start_time = time.time()
            results = self.devo_client.execute_query(query, timeout=timeout, table_name=table_name)
            duration = time.time() - start_time

            row_count = len(results) if results else 0
            self.log_step("QUERY", f"Verification query: {row_count} rows in {duration:.1f}s")

            return results

        except Exception as e:
            self.log_step("QUERY", f"Verification query failed: {str(e)}", "WARNING")
            return []

    def _query_table_data_streaming(self, table_name: str, target_date: datetime, total_rows: int) -> List[Dict[str, Any]]:
        """Streaming query method for large datasets."""
        if not self.devo_client:
            raise RuntimeError("Devo client not initialized")
        if not self.streaming_processor:
            raise RuntimeError("Streaming processor not initialized")

        date_str = target_date.strftime("%Y-%m-%d")
        next_date_str = (target_date + timedelta(days=1)).strftime('%Y-%m-%d')
        where_clause = f"eventdate >= '{date_str}' and eventdate < '{next_date_str}'"

        self.log_step("QUERY", f"Using streaming processing for {table_name}")
        self.log_step("QUERY", f"Total rows: {total_rows:,}")

        # Set up streaming query
        streaming_info = self.devo_client.execute_streaming_query(
            table_name=table_name,
            chunk_size=self.streaming_config.default_chunk_size,
            where_clause=where_clause,
            timeout=self.get_timeout_for_table(table_name)
        )

        if streaming_info['total_rows'] == 0:
            self.log_step("QUERY", "No data found in streaming setup")
            return []

        # Create temporary file for streaming results
        temp_filename = f"{table_name}_{date_str}_streaming.json"
        temp_path = os.path.join('temp', temp_filename)
        os.makedirs('temp', exist_ok=True)

        # Process data using streaming processor
        streaming_result = self.streaming_processor.process_data_streaming(
            query_func=streaming_info['query_function'],
            total_rows=streaming_info['total_rows'],
            output_path=temp_path,
            table_name=table_name
        )

        if streaming_result['status'] == 'completed':
            # Load the final merged file
            try:
                with open(streaming_result['output_file'], 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.log_step("QUERY", f"✅ Streaming completed: {len(data):,} rows in {streaming_result['processing_time']:.1f}s")

                # Clean up the streaming output file (we'll create our own temp file)
                if os.path.exists(streaming_result['output_file']):
                    os.unlink(streaming_result['output_file'])

                return data

            except Exception as e:
                self.log_step("QUERY", f"❌ Failed to load streaming results: {str(e)}", "ERROR")
                raise
        else:
            error_msg = f"Streaming failed: {streaming_result.get('failed_chunks', 0)} chunks failed"
            self.log_step("QUERY", f"❌ {error_msg}", "ERROR")
            raise RuntimeError(error_msg)
    
    def save_to_temp_file(self, data: List[Dict[str, Any]], table_name: str, target_date: datetime) -> str:
        """Save data to temporary file."""
        date_str = target_date.strftime('%Y-%m-%d')
        temp_filename = f"{table_name}_{date_str}.json"
        temp_path = os.path.join('temp', temp_filename)
        
        # Create temp directory
        os.makedirs('temp', exist_ok=True)
        
        self.log_step("SAVE", f"Saving {len(data):,} rows to {temp_filename}")
        
        start_time = time.time()
        with open(temp_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, separators=(',', ':'))
        
        duration = time.time() - start_time
        file_size_mb = os.path.getsize(temp_path) / (1024 * 1024)
        
        self.log_step("SAVE", f"✅ File saved: {file_size_mb:.1f}MB in {duration:.1f}s")
        
        self.temp_files.append(temp_path)
        return temp_path
    
    def upload_to_oss(self, temp_file: str, table_name: str, target_date: datetime) -> bool:
        """Upload file to OSS with simple retry logic."""
        if not self.storage_manager:
            raise RuntimeError("Storage manager not initialized")

        date_str = target_date.strftime('%Y-%m-%d')
        month_name = target_date.strftime('%B')
        week_number = (target_date.day - 1) // 7 + 1

        oss_path = f"Devo/{month_name}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz"

        self.log_step("UPLOAD", f"Uploading to OSS: {oss_path}")
        
        for attempt in range(self.MAX_RETRIES + 1):
            try:
                if attempt > 0:
                    self.log_step("RETRY", f"Upload attempt {attempt + 1}/{self.MAX_RETRIES + 1}")
                
                # Create temporary directory for compression
                temp_upload_dir = tempfile.mkdtemp(prefix='upload_')
                temp_file_in_dir = os.path.join(temp_upload_dir, os.path.basename(temp_file))
                shutil.copy2(temp_file, temp_file_in_dir)
                
                try:
                    start_time = time.time()
                    success, details = self.storage_manager.compress_and_upload(
                        temp_upload_dir, oss_path, verify_integrity=True
                    )
                    duration = time.time() - start_time
                    
                    if success:
                        self.log_step("UPLOAD", f"✅ Upload successful in {duration:.1f}s")
                        return True
                    else:
                        raise Exception(f"Upload failed: {details}")
                        
                finally:
                    # Clean up temp upload directory
                    shutil.rmtree(temp_upload_dir, ignore_errors=True)
                
            except Exception as e:
                if attempt < self.MAX_RETRIES:
                    self.log_step("RETRY", f"❌ Upload failed: {str(e)}", "WARNING")
                    self.log_step("RETRY", f"Waiting {self.RETRY_DELAY} seconds before retry...")
                    time.sleep(self.RETRY_DELAY)
                else:
                    self.log_step("UPLOAD", f"❌ All upload attempts failed: {str(e)}", "ERROR")
                    return False
        
        return False
    
    def cleanup_temp_file(self, temp_file: str):
        """Clean up temporary file."""
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                self.log_step("CLEANUP", f"✅ Removed temp file: {os.path.basename(temp_file)}")
                if temp_file in self.temp_files:
                    self.temp_files.remove(temp_file)
        except Exception as e:
            self.log_step("CLEANUP", f"⚠️  Failed to remove temp file: {str(e)}", "WARNING")
    
    def cleanup_all_temp_files(self):
        """Clean up all temporary files."""
        self.log_step("CLEANUP", "Cleaning up all temporary files...")
        for temp_file in self.temp_files.copy():
            self.cleanup_temp_file(temp_file)

    def backup_single_table(self, table_name: str, target_date: datetime) -> Dict[str, Any]:
        """Backup a single table for a specific date."""
        start_time = time.time()
        date_str = target_date.strftime('%Y-%m-%d')

        self.log_step("TABLE_START", f"Starting backup: {table_name} for {date_str}")

        result = {
            'table_name': table_name,
            'date': date_str,
            'status': 'started',
            'rows': 0,
            'duration': 0,
            'error': None
        }

        temp_file = None

        try:
            # Step 1: Query data
            self.log_step("STEP_1", f"Querying data from Devo API...")
            data = self.query_table_data(table_name, target_date)
            result['rows'] = len(data) if data else 0

            if result['rows'] == 0:
                self.log_step("NO_DATA", f"No data found for {table_name} on {date_str}")
                result['status'] = 'no_data'
                result['duration'] = time.time() - start_time
                return result

            # Step 2: Save to temp file
            self.log_step("STEP_2", f"Saving {result['rows']:,} rows to temporary file...")
            temp_file = self.save_to_temp_file(data, table_name, target_date)

            # Step 3: Upload to OSS
            self.log_step("STEP_3", f"Uploading to OSS...")
            upload_success = self.upload_to_oss(temp_file, table_name, target_date)

            if upload_success:
                result['status'] = 'completed'
                self.log_step("TABLE_SUCCESS", f"✅ {table_name} backup completed successfully")
            else:
                result['status'] = 'upload_failed'
                result['error'] = 'Upload to OSS failed'
                self.log_step("TABLE_FAILED", f"❌ {table_name} backup failed - upload error")

        except Exception as e:
            result['status'] = 'failed'
            result['error'] = str(e)
            self.log_step("TABLE_FAILED", f"❌ {table_name} backup failed: {str(e)}", "ERROR")

        finally:
            # Step 4: Cleanup temp file
            if temp_file:
                self.log_step("STEP_4", f"Cleaning up temporary file...")
                self.cleanup_temp_file(temp_file)

            result['duration'] = time.time() - start_time
            self.log_step("TABLE_END", f"Table {table_name} completed in {result['duration']:.1f}s")

        return result

    def backup_date(self, tables: List[str], target_date: datetime) -> Dict[str, Any]:
        """Backup all tables for a specific date."""
        date_str = target_date.strftime('%Y-%m-%d')
        start_time = time.time()

        self.log_step("DATE_START", f"Starting backup for date: {date_str}")
        self.log_step("DATE_INFO", f"Tables to process: {len(tables)}")

        results = {
            'date': date_str,
            'total_tables': len(tables),
            'completed': 0,
            'failed': 0,
            'no_data': 0,
            'total_rows': 0,
            'duration': 0,
            'table_results': []
        }

        for i, table_name in enumerate(tables, 1):
            self.log_step("PROGRESS", f"Processing table {i}/{len(tables)}: {table_name}")

            table_result = self.backup_single_table(table_name, target_date)
            results['table_results'].append(table_result)

            # Update counters
            if table_result['status'] == 'completed':
                results['completed'] += 1
                results['total_rows'] += table_result['rows']
            elif table_result['status'] == 'no_data':
                results['no_data'] += 1
            else:
                results['failed'] += 1

            # Show progress
            remaining = len(tables) - i
            if remaining > 0:
                avg_time = (time.time() - start_time) / i
                estimated_remaining = remaining * avg_time / 60
                self.log_step("PROGRESS", f"Remaining: {remaining} tables (~{estimated_remaining:.1f} minutes)")

        results['duration'] = time.time() - start_time

        # Summary
        self.log_step("DATE_SUMMARY", f"Date {date_str} completed:")
        self.log_step("DATE_SUMMARY", f"  ✅ Completed: {results['completed']}")
        self.log_step("DATE_SUMMARY", f"  📭 No data: {results['no_data']}")
        self.log_step("DATE_SUMMARY", f"  ❌ Failed: {results['failed']}")
        self.log_step("DATE_SUMMARY", f"  📊 Total rows: {results['total_rows']:,}")
        self.log_step("DATE_SUMMARY", f"  ⏱️  Duration: {results['duration']/60:.1f} minutes")

        return results

    def run_backup(self, dates: List[datetime], tables: List[str]) -> Dict[str, Any]:
        """Run backup for multiple dates."""
        overall_start_time = time.time()

        self.log_step("BACKUP_START", "=== TNGD BACKUP SYSTEM STARTED ===")
        self.log_step("BACKUP_INFO", f"Log file: {self.log_file_path}")
        self.log_step("BACKUP_INFO", f"Dates to process: {len(dates)}")
        self.log_step("BACKUP_INFO", f"Tables per date: {len(tables)}")
        self.log_step("BACKUP_INFO", f"Total operations: {len(dates) * len(tables)}")

        # Test connections first
        if not self.test_connections():
            self.log_step("BACKUP_FAILED", "❌ Connection tests failed. Aborting backup.", "ERROR")

            # Try to send failure email if email service is available
            connection_failed_results = {
                'status': 'connection_failed',
                'total_dates': len(dates),
                'total_tables': len(tables),
                'date_results': [],
                'overall_duration': time.time() - overall_start_time,
                'total_rows_backed_up': 0,
                'error': 'Connection tests failed'
            }

            if self.email_service:
                try:
                    self.email_service.send_backup_summary(connection_failed_results)
                    self.log_step("EMAIL_SUMMARY", "✅ Failure notification email sent")
                except Exception as e:
                    self.log_step("EMAIL_SUMMARY", f"⚠️  Failed to send failure email: {str(e)}", "WARNING")

            return connection_failed_results

        overall_results = {
            'status': 'completed',
            'total_dates': len(dates),
            'total_tables': len(tables),
            'date_results': [],
            'overall_duration': 0,
            'total_rows_backed_up': 0
        }

        try:
            for i, target_date in enumerate(dates, 1):
                self.log_step("DATE_PROGRESS", f"Processing date {i}/{len(dates)}: {target_date.strftime('%Y-%m-%d')}")

                date_result = self.backup_date(tables, target_date)
                overall_results['date_results'].append(date_result)
                overall_results['total_rows_backed_up'] += date_result['total_rows']

                # Show overall progress
                remaining_dates = len(dates) - i
                if remaining_dates > 0:
                    avg_time_per_date = (time.time() - overall_start_time) / i
                    estimated_remaining = remaining_dates * avg_time_per_date / 60
                    self.log_step("OVERALL_PROGRESS", f"Remaining dates: {remaining_dates} (~{estimated_remaining:.1f} minutes)")

        except Exception as e:
            overall_results['status'] = 'failed'
            overall_results['error'] = str(e)
            self.log_step("BACKUP_FAILED", f"❌ Backup failed: {str(e)}", "ERROR")

        finally:
            # Final cleanup
            self.cleanup_all_temp_files()

            overall_results['overall_duration'] = time.time() - overall_start_time

            # Final summary
            self.log_step("BACKUP_SUMMARY", "=== BACKUP COMPLETED ===")
            self.log_step("BACKUP_SUMMARY", f"Status: {overall_results['status']}")
            self.log_step("BACKUP_SUMMARY", f"Total rows backed up: {overall_results['total_rows_backed_up']:,}")
            self.log_step("BACKUP_SUMMARY", f"Total duration: {overall_results['overall_duration']/60:.1f} minutes")

            # Send email summary if email service is available
            if self.email_service:
                self.log_step("EMAIL_SUMMARY", "Sending backup summary email...")
                try:
                    if self.email_service.send_backup_summary(overall_results):
                        self.log_step("EMAIL_SUMMARY", "✅ Backup summary email sent successfully")
                    else:
                        self.log_step("EMAIL_SUMMARY", "⚠️  Failed to send backup summary email", "WARNING")
                except Exception as e:
                    self.log_step("EMAIL_SUMMARY", f"⚠️  Error sending email: {str(e)}", "WARNING")
            else:
                self.log_step("EMAIL_SUMMARY", "📧 Email service not available - skipping email summary")

        return overall_results


def parse_dates(args: List[str]) -> List[datetime]:
    """Parse command line arguments to get dates."""
    if not args:
        # No arguments - use today
        return [datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)]

    if len(args) == 1:
        # Single date
        try:
            date = datetime.strptime(args[0], '%Y-%m-%d')
            return [date]
        except ValueError:
            print(f"❌ Invalid date format: {args[0]}. Use YYYY-MM-DD")
            sys.exit(1)

    if len(args) == 2:
        # Date range
        try:
            start_date = datetime.strptime(args[0], '%Y-%m-%d')
            end_date = datetime.strptime(args[1], '%Y-%m-%d')

            if start_date > end_date:
                print("❌ Start date cannot be after end date")
                sys.exit(1)

            dates = []
            current_date = start_date
            while current_date <= end_date:
                dates.append(current_date)
                current_date += timedelta(days=1)

            return dates

        except ValueError as e:
            print(f"❌ Invalid date format: {e}. Use YYYY-MM-DD")
            sys.exit(1)

    print("❌ Invalid arguments. Usage:")
    print("  python tngd_backup.py                    # Today")
    print("  python tngd_backup.py 2025-03-01         # Single date")
    print("  python tngd_backup.py 2025-03-01 2025-03-31  # Date range")
    sys.exit(1)


def main():
    """Main function."""
    try:
        # Parse command line arguments
        dates = parse_dates(sys.argv[1:])

        # Create backup system with dates for unique log naming
        backup = TngdBackup(dates)

        # Load tables
        tables = backup.load_tables()

        # Run backup
        results = backup.run_backup(dates, tables)

        # Exit with appropriate code
        if results.get('status') == 'completed':
            sys.exit(0)
        else:
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n❌ Backup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
