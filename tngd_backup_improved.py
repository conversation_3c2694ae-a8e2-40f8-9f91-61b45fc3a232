#!/usr/bin/env python3
"""
TNGD Backup System - Improved Version
=====================================

Enhanced backup system with better resource management, thread control,
and error handling to prevent incomplete backups.

Key Improvements:
- Thread pool management to prevent "can't start new thread" errors
- Memory monitoring and throttling
- Better error recovery and retry logic
- Resource cleanup and garbage collection
- Progress checkpointing for resume capability

Usage:
    python tngd_backup_improved.py                    # Today's data
    python tngd_backup_improved.py 2025-03-01         # Single date
    python tngd_backup_improved.py 2025-03-01 2025-03-31  # Date range
"""

import os
import sys
import time
import json
import logging
import tempfile
import threading
import gc
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config_manager import ConfigManager
from core.devo_client import DevoClient
from core.storage_manager import StorageManager
from core.email_service import EmailService
from core.streaming_processor import StreamingDataProcessor, StreamingConfig
from core.thread_manager import get_thread_manager, managed_thread_pool, log_thread_metrics


@dataclass
class BackupCheckpoint:
    """Checkpoint data for backup resume capability."""
    backup_id: str
    start_time: datetime
    dates: List[str]
    tables: List[str]
    completed_tables: Dict[str, List[str]]  # date -> [table_names]
    failed_tables: Dict[str, List[str]]     # date -> [table_names]
    current_date: Optional[str] = None
    current_table: Optional[str] = None


class ImprovedTngdBackup:
    """Improved TNGD backup system with enhanced resource management."""
    
    def __init__(self, dates: Optional[List[datetime]] = None):
        """Initialize the improved backup system."""
        self.dates = dates or []
        self.backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Setup logging
        self.log_file_path = self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize checkpoint system
        self.checkpoint_file = f"checkpoints/{self.backup_id}.json"
        os.makedirs("checkpoints", exist_ok=True)
        
        # Initialize clients
        self.config_manager = ConfigManager()
        self.devo_client = None
        self.storage_manager = None
        self.email_service = None
        self.streaming_processor = None
        
        # Resource management settings
        self.MAX_RETRIES = 3
        self.RETRY_DELAY = 60
        self.QUERY_TIMEOUT = 1800  # 30 minutes
        self.LARGE_TABLE_TIMEOUT = 3600  # 1 hour
        self.MEMORY_THRESHOLD_MB = 2000  # 2GB memory threshold
        self.THREAD_LIMIT = 6  # Conservative thread limit
        
        # Resource monitoring
        self.thread_manager = get_thread_manager()
        self.resource_check_interval = 300  # 5 minutes
        self.last_resource_check = 0
        
        # Checkpoint data
        self.checkpoint = BackupCheckpoint(
            backup_id=self.backup_id,
            start_time=datetime.now(),
            dates=[d.strftime('%Y-%m-%d') for d in self.dates],
            tables=[],
            completed_tables={},
            failed_tables={}
        )

        # Track temporary files for cleanup
        self.temp_files = []
        
        self.logger.info(f"ImprovedTngdBackup initialized: {self.backup_id}")
    
    def setup_logging(self):
        """Setup enhanced logging with resource monitoring."""
        os.makedirs('logs', exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        date_info = ""
        if self.dates:
            if len(self.dates) == 1:
                date_info = f"_{self.dates[0].strftime('%Y-%m-%d')}"
            else:
                start_date = self.dates[0].strftime('%Y-%m-%d')
                end_date = self.dates[-1].strftime('%Y-%m-%d')
                date_info = f"_{start_date}_to_{end_date}"
        
        log_filename = f"tngd_backup_improved_{timestamp}{date_info}.log"
        log_file_path = os.path.join('logs', log_filename)
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file_path, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return log_file_path
    
    def save_checkpoint(self):
        """Save current backup progress to checkpoint file."""
        try:
            with open(self.checkpoint_file, 'w') as f:
                # Convert datetime objects to strings for JSON serialization
                checkpoint_data = asdict(self.checkpoint)
                checkpoint_data['start_time'] = self.checkpoint.start_time.isoformat()
                json.dump(checkpoint_data, f, indent=2)
            
            self.logger.debug(f"Checkpoint saved: {self.checkpoint_file}")
        except Exception as e:
            self.logger.error(f"Failed to save checkpoint: {e}")
    
    def load_checkpoint(self, checkpoint_file: str) -> Optional[BackupCheckpoint]:
        """Load backup checkpoint for resume capability."""
        try:
            if not os.path.exists(checkpoint_file):
                return None
            
            with open(checkpoint_file, 'r') as f:
                data = json.load(f)
            
            # Convert string back to datetime
            data['start_time'] = datetime.fromisoformat(data['start_time'])
            
            return BackupCheckpoint(**data)
        except Exception as e:
            self.logger.error(f"Failed to load checkpoint: {e}")
            return None
    
    def check_resources(self):
        """Check system resources and apply throttling if needed."""
        current_time = time.time()
        if current_time - self.last_resource_check < self.resource_check_interval:
            return
        
        self.last_resource_check = current_time
        
        try:
            # Log thread metrics
            log_thread_metrics()
            
            # Get resource metrics
            metrics = self.thread_manager.get_metrics()
            
            # Check memory usage
            if metrics.memory_usage_mb > self.MEMORY_THRESHOLD_MB:
                self.logger.warning(f"High memory usage: {metrics.memory_usage_mb:.1f}MB")
                
                # Force garbage collection
                gc.collect()
                
                # Apply throttling
                time.sleep(5)
            
            # Check thread count
            if metrics.active_threads > self.THREAD_LIMIT * 2:
                self.logger.warning(f"High thread count: {metrics.active_threads}")
                
                # Force cleanup
                self.thread_manager._force_cleanup()
                time.sleep(2)
        
        except Exception as e:
            self.logger.error(f"Resource check failed: {e}")
    
    def initialize_clients(self):
        """Initialize all client connections with improved error handling."""
        self.log_step("INIT", "Initializing clients...")
        
        try:
            # Initialize Devo client
            self.devo_client = DevoClient()
            self.log_step("INIT", "Devo client initialized")

            # Initialize storage manager
            self.storage_manager = StorageManager(self.config_manager)
            self.log_step("INIT", "Storage manager initialized")
            
            # Initialize email service
            try:
                self.email_service = EmailService()
                self.log_step("INIT", "Email service initialized")
            except Exception as email_error:
                self.log_step("INIT", f"Email service initialization failed: {email_error}", "WARNING")
                self.email_service = None
            
            # Initialize streaming processor
            streaming_config = StreamingConfig(
                default_chunk_size=50000,  # Smaller chunks for better memory management
                streaming_threshold_rows=100000,  # Lower threshold
                memory_threshold_mb=self.MEMORY_THRESHOLD_MB
            )
            self.streaming_processor = StreamingDataProcessor(streaming_config, self.log_step)
            self.log_step("INIT", "Streaming processor initialized")
            
            return True
            
        except Exception as e:
            self.log_step("INIT", f"[ERROR] Client initialization failed: {e}", "ERROR")
            return False
    
    def log_step(self, step: str, message: str, level: str = "INFO"):
        """Enhanced logging with resource monitoring."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {step}: {message}"
        
        if level == "ERROR":
            self.logger.error(formatted_message)
        elif level == "WARNING":
            self.logger.warning(formatted_message)
        else:
            self.logger.info(formatted_message)
        
        # Periodic resource checks
        if step in ["TABLE_START", "TABLE_END", "DATE_START", "DATE_END"]:
            self.check_resources()
    
    def load_tables(self) -> List[str]:
        """Load table list from configuration."""
        self.log_step("CONFIG", "Loading table list...")

        # Try multiple possible locations for tables.json
        table_paths = [
            'config/tables.json',
            'tables.json',
            'config/table_list.json'
        ]

        for table_path in table_paths:
            try:
                if os.path.exists(table_path):
                    with open(table_path, 'r') as f:
                        tables = json.load(f)

                    self.log_step("CONFIG", f"Loaded {len(tables)} tables from {table_path}")
                    self.checkpoint.tables = tables
                    self.save_checkpoint()
                    return tables
            except Exception as e:
                self.log_step("CONFIG", f"Failed to load from {table_path}: {str(e)}", "WARNING")
                continue

        # Fallback to basic tables if no config file found
        fallback_tables = [
            'my.app.tngd.polardb',
            'cloud.office365.management.exchange',
            'firewall.fortinet.traffic.forward'
        ]

        self.log_step("CONFIG", f"Using fallback table list: {len(fallback_tables)} tables", "WARNING")
        self.checkpoint.tables = fallback_tables
        self.save_checkpoint()
        return fallback_tables

    def backup_single_table_improved(self, table_name: str, target_date: datetime) -> Dict[str, Any]:
        """Improved single table backup with better resource management."""
        date_str = target_date.strftime('%Y-%m-%d')

        # Update checkpoint
        self.checkpoint.current_date = date_str
        self.checkpoint.current_table = table_name
        self.save_checkpoint()

        self.log_step("TABLE_START", f"Starting backup: {table_name} for {date_str}")

        result = {
            'table_name': table_name,
            'date': date_str,
            'status': 'failed',
            'rows': 0,
            'error': None,
            'duration': 0,
            'retry_count': 0
        }

        start_time = time.time()

        try:
            # Use managed thread pool for this table
            with managed_thread_pool(f"table-{table_name}", max_workers=2) as pool:

                # Step 1: Query data with improved error handling
                self.log_step("STEP_1", f"Querying data from Devo API...")

                for attempt in range(self.MAX_RETRIES + 1):
                    try:
                        result['retry_count'] = attempt

                        # Check resources before each attempt
                        self.check_resources()

                        # Query data
                        data = self.query_table_data_improved(table_name, target_date)

                        if not data:
                            result['status'] = 'no_data'
                            self.log_step("NO_DATA", f"No data found for {table_name} on {date_str}")
                            break

                        result['rows'] = len(data)
                        self.log_step("QUERY", f"[OK] Retrieved {result['rows']:,} rows")

                        # Step 2: Save to temp file
                        self.log_step("STEP_2", f"Saving {result['rows']:,} rows to temporary file...")
                        temp_file = self.save_to_temp_file_improved(data, table_name, target_date)

                        # Step 3: Upload to OSS
                        self.log_step("STEP_3", f"Uploading to OSS...")
                        upload_success = self.upload_to_oss_improved(temp_file, table_name, target_date)

                        if upload_success:
                            result['status'] = 'completed'
                            self.log_step("TABLE_SUCCESS", f"[OK] {table_name} backup completed successfully")

                            # Update checkpoint
                            if date_str not in self.checkpoint.completed_tables:
                                self.checkpoint.completed_tables[date_str] = []
                            self.checkpoint.completed_tables[date_str].append(table_name)

                        else:
                            result['status'] = 'upload_failed'
                            result['error'] = 'Upload to OSS failed'
                            self.log_step("TABLE_FAILED", f"[ERROR] {table_name} backup failed - upload error")

                        # Clean up temp file
                        self.cleanup_temp_file(temp_file)
                        break

                    except Exception as e:
                        error_msg = str(e)
                        result['error'] = error_msg

                        if attempt < self.MAX_RETRIES:
                            self.log_step("RETRY", f"[ERROR] Attempt {attempt + 1} failed: {error_msg}", "WARNING")
                            self.log_step("RETRY", f"Waiting {self.RETRY_DELAY} seconds before retry...")

                            # Force resource cleanup before retry
                            gc.collect()
                            time.sleep(self.RETRY_DELAY)
                        else:
                            self.log_step("TABLE_FAILED", f"[ERROR] {table_name} backup failed after {self.MAX_RETRIES + 1} attempts: {error_msg}", "ERROR")

                            # Update checkpoint with failed table
                            if date_str not in self.checkpoint.failed_tables:
                                self.checkpoint.failed_tables[date_str] = []
                            self.checkpoint.failed_tables[date_str].append(table_name)

        except Exception as e:
            result['error'] = str(e)
            self.log_step("TABLE_FAILED", f"[ERROR] {table_name} backup failed: {str(e)}", "ERROR")

        finally:
            result['duration'] = time.time() - start_time
            self.log_step("TABLE_END", f"Table {table_name} completed in {result['duration']:.1f}s")

            # Clear current table from checkpoint
            self.checkpoint.current_table = None
            self.save_checkpoint()

        return result

    def query_table_data_improved(self, table_name: str, target_date: datetime) -> List[Dict[str, Any]]:
        """Improved data querying with better resource management."""
        date_str = target_date.strftime('%Y-%m-%d')

        # Build query
        query = f"""
        from {table_name}
        where eventdate >= "{date_str} 00:00:00" and eventdate <= "{date_str} 23:59:59"
        """

        # Determine timeout based on table size
        timeout = self.LARGE_TABLE_TIMEOUT if self.is_large_table(table_name) else self.QUERY_TIMEOUT

        try:
            # Use streaming processor for large datasets
            if self.streaming_processor and self.streaming_processor.should_use_streaming(estimated_rows=100000, table_name=table_name):
                self.log_step("QUERY", f"Using streaming processing for {table_name}")
                return self.query_with_streaming(query, table_name, timeout)
            else:
                self.log_step("QUERY", f"Using standard processing for {table_name}")
                return self.devo_client.execute_query(query, timeout=timeout, table_name=table_name)

        except Exception as e:
            self.log_step("QUERY", f"[ERROR] Query failed: {str(e)}", "ERROR")
            raise

    def is_large_table(self, table_name: str) -> bool:
        """Check if table is known to be large."""
        large_tables = [
            'cef0.zscaler.nssweblog',
            'cloud.alibaba.log_service.events',
            'cloud.office365.management.exchange',
            'firewall.fortinet.traffic.forward',
            'cloud.office365.management.endpoint',
            'my.app.tngd.polardb'
        ]
        return table_name in large_tables

    def query_with_streaming(self, query: str, table_name: str, timeout: int) -> List[Dict[str, Any]]:
        """Query data using streaming processor."""
        try:
            # Use streaming processor for large datasets
            def query_chunk(offset: int, limit: int) -> List[Dict[str, Any]]:
                return self.devo_client.execute_query_chunked(
                    query, offset, limit, timeout=timeout, table_name=table_name
                )

            # Estimate total rows (simplified approach)
            sample_data = self.devo_client.execute_query_chunked(query, 0, 1000, timeout=60, table_name=table_name)
            estimated_rows = len(sample_data) * 10  # Rough estimate

            if estimated_rows < 1000:
                # Small dataset, use standard query
                return self.devo_client.execute_query(query, timeout=timeout, table_name=table_name)

            # Use streaming for large datasets
            temp_file = f"temp/{table_name}_streaming.json"
            os.makedirs("temp", exist_ok=True)

            result = self.streaming_processor.process_data_streaming(
                query_func=query_chunk,
                total_rows=estimated_rows,
                output_path=temp_file,
                table_name=table_name
            )

            if result['status'] == 'completed':
                # Load data from temp file
                with open(temp_file, 'r') as f:
                    return [json.loads(line) for line in f if line.strip()]
            else:
                # Fallback to standard query
                return self.devo_client.execute_query(query, timeout=timeout, table_name=table_name)

        except Exception as e:
            self.log_step("STREAMING", f"Streaming failed, falling back to standard query: {e}", "WARNING")
            return self.devo_client.execute_query(query, timeout=timeout, table_name=table_name)

    def save_to_temp_file_improved(self, data: List[Dict[str, Any]], table_name: str, target_date: datetime) -> str:
        """Save data to temporary file with improved error handling."""
        date_str = target_date.strftime('%Y-%m-%d')

        # Create temp directory
        os.makedirs("temp", exist_ok=True)

        # Generate temp file name
        temp_file = f"temp/{table_name}_{date_str}.json"

        try:
            with open(temp_file, 'w', encoding='utf-8') as f:
                for row in data:
                    json.dump(row, f, ensure_ascii=False)
                    f.write('\n')

            self.temp_files.append(temp_file)
            file_size_mb = os.path.getsize(temp_file) / (1024 * 1024)
            self.log_step("TEMP_FILE", f"[OK] Saved {len(data):,} rows to {temp_file} ({file_size_mb:.1f}MB)")

            return temp_file

        except Exception as e:
            self.log_step("TEMP_FILE", f"[ERROR] Failed to save temp file: {e}", "ERROR")
            raise

    def upload_to_oss_improved(self, temp_file: str, table_name: str, target_date: datetime) -> bool:
        """Upload file to OSS with improved error handling."""
        date_str = target_date.strftime('%Y-%m-%d')

        # Generate OSS path
        year = target_date.year
        month_name = target_date.strftime('%B')
        week_num = (target_date.day - 1) // 7 + 1
        oss_path = f"Devo/{month_name}/week {week_num}/{date_str}/{table_name}_{date_str}.tar.gz"

        try:
            self.log_step("UPLOAD", f"Uploading to OSS: {oss_path}")

            # Use storage manager with retry
            for attempt in range(self.MAX_RETRIES + 1):
                try:
                    success, details = self.storage_manager.compress_and_upload(
                        temp_file, oss_path, verify_integrity=True
                    )

                    if success:
                        self.log_step("UPLOAD", f"[OK] Upload successful")
                        return True
                    else:
                        raise Exception(f"Upload failed: {details}")

                except Exception as e:
                    if attempt < self.MAX_RETRIES:
                        self.log_step("UPLOAD", f"[ERROR] Upload attempt {attempt + 1} failed: {e}", "WARNING")
                        time.sleep(self.RETRY_DELAY)
                    else:
                        self.log_step("UPLOAD", f"[ERROR] All upload attempts failed: {e}", "ERROR")
                        return False

            return False

        except Exception as e:
            self.log_step("UPLOAD", f"[ERROR] Upload error: {e}", "ERROR")
            return False

    def cleanup_temp_file(self, temp_file: str):
        """Clean up temporary file with improved error handling."""
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                self.log_step("CLEANUP", f"[OK] Removed temp file: {os.path.basename(temp_file)}")
                if temp_file in self.temp_files:
                    self.temp_files.remove(temp_file)
        except Exception as e:
            self.log_step("CLEANUP", f"[WARNING] Failed to remove temp file: {e}", "WARNING")

    def backup_date_improved(self, tables: List[str], target_date: datetime) -> Dict[str, Any]:
        """Backup all tables for a specific date with improved resource management."""
        date_str = target_date.strftime('%Y-%m-%d')

        self.log_step("DATE_START", f"Starting backup for date: {date_str}")
        self.log_step("DATE_INFO", f"Tables to process: {len(tables)}")

        # Update checkpoint
        self.checkpoint.current_date = date_str
        self.save_checkpoint()

        results = {
            'date': date_str,
            'completed': 0,
            'no_data': 0,
            'failed': 0,
            'total_rows': 0,
            'table_results': []
        }

        start_time = time.time()

        for i, table_name in enumerate(tables, 1):
            self.log_step("PROGRESS", f"Processing table {i}/{len(tables)}: {table_name}")

            # Check if table already completed (resume capability)
            if (date_str in self.checkpoint.completed_tables and
                table_name in self.checkpoint.completed_tables[date_str]):
                self.log_step("SKIP", f"Table {table_name} already completed, skipping")
                results['completed'] += 1
                continue

            # Check if table previously failed
            if (date_str in self.checkpoint.failed_tables and
                table_name in self.checkpoint.failed_tables[date_str]):
                self.log_step("RETRY", f"Retrying previously failed table: {table_name}")

            # Backup single table
            table_result = self.backup_single_table_improved(table_name, target_date)
            results['table_results'].append(table_result)

            # Update counters
            if table_result['status'] == 'completed':
                results['completed'] += 1
                results['total_rows'] += table_result['rows']
            elif table_result['status'] == 'no_data':
                results['no_data'] += 1
            else:
                results['failed'] += 1

            # Show progress
            remaining = len(tables) - i
            if remaining > 0:
                avg_time = (time.time() - start_time) / i
                estimated_remaining = remaining * avg_time / 60
                self.log_step("PROGRESS", f"Remaining: {remaining} tables (~{estimated_remaining:.1f} minutes)")

            # Periodic resource check and cleanup
            if i % 5 == 0:  # Every 5 tables
                self.check_resources()
                gc.collect()  # Force garbage collection

        # Calculate duration
        duration = time.time() - start_time

        # Log summary
        self.log_step("DATE_SUMMARY", f"Date {date_str} completed:")
        self.log_step("DATE_SUMMARY", f"  [OK] Completed: {results['completed']}")
        self.log_step("DATE_SUMMARY", f"  [NO_DATA] No data: {results['no_data']}")
        self.log_step("DATE_SUMMARY", f"  [ERROR] Failed: {results['failed']}")
        self.log_step("DATE_SUMMARY", f"  [DATA] Total rows: {results['total_rows']:,}")
        self.log_step("DATE_SUMMARY", f"  [TIME] Duration: {duration/60:.1f} minutes")

        # Clear current date from checkpoint
        self.checkpoint.current_date = None
        self.save_checkpoint()

        return results

    def run_improved_backup(self, dates: List[datetime], tables: List[str]) -> Dict[str, Any]:
        """Run the improved backup process."""
        self.log_step("BACKUP_START", "=== IMPROVED TNGD BACKUP SYSTEM STARTED ===")
        self.log_step("BACKUP_INFO", f"Log file: {self.log_file_path}")
        self.log_step("BACKUP_INFO", f"Dates to process: {len(dates)}")
        self.log_step("BACKUP_INFO", f"Tables per date: {len(tables)}")
        self.log_step("BACKUP_INFO", f"Total operations: {len(dates) * len(tables)}")

        overall_start_time = time.time()
        overall_results = {
            'status': 'completed',
            'total_dates': len(dates),
            'total_tables': len(tables),
            'date_results': [],
            'total_rows_backed_up': 0,
            'error': None
        }

        try:
            # Initialize clients
            if not self.initialize_clients():
                raise Exception("Failed to initialize clients")

            # Process each date
            for i, target_date in enumerate(dates, 1):
                self.log_step("DATE_PROGRESS", f"Processing date {i}/{len(dates)}: {target_date.strftime('%Y-%m-%d')}")

                date_result = self.backup_date_improved(tables, target_date)
                overall_results['date_results'].append(date_result)
                overall_results['total_rows_backed_up'] += date_result['total_rows']

                # Show overall progress
                remaining_dates = len(dates) - i
                if remaining_dates > 0:
                    avg_time_per_date = (time.time() - overall_start_time) / i
                    estimated_remaining = remaining_dates * avg_time_per_date / 60
                    self.log_step("OVERALL_PROGRESS", f"Remaining dates: {remaining_dates} (~{estimated_remaining:.1f} minutes)")

        except Exception as e:
            overall_results['status'] = 'failed'
            overall_results['error'] = str(e)
            self.log_step("BACKUP_FAILED", f"[ERROR] Backup failed: {str(e)}", "ERROR")

        finally:
            # Final cleanup
            self.cleanup_all_temp_files()

            # Calculate total duration
            overall_results['overall_duration'] = time.time() - overall_start_time

            # Final summary
            self.log_step("BACKUP_SUMMARY", "=== IMPROVED BACKUP COMPLETED ===")
            self.log_step("BACKUP_SUMMARY", f"Status: {overall_results['status']}")
            self.log_step("BACKUP_SUMMARY", f"Total rows backed up: {overall_results['total_rows_backed_up']:,}")
            self.log_step("BACKUP_SUMMARY", f"Total duration: {overall_results['overall_duration']/60:.1f} minutes")

            # Send email summary if available
            if self.email_service:
                try:
                    self.email_service.send_backup_summary(overall_results)
                    self.log_step("EMAIL_SUMMARY", "[OK] Backup summary email sent")
                except Exception as e:
                    self.log_step("EMAIL_SUMMARY", f"[WARNING] Failed to send email: {e}", "WARNING")

        return overall_results

    def cleanup_all_temp_files(self):
        """Clean up all temporary files."""
        self.log_step("CLEANUP", "Cleaning up all temporary files...")
        for temp_file in self.temp_files.copy():
            self.cleanup_temp_file(temp_file)

        # Force garbage collection
        gc.collect()


def parse_dates(args: List[str]) -> List[datetime]:
    """Parse command line date arguments."""
    if len(args) == 0:
        # Default to today
        return [datetime.now()]

    if len(args) == 1:
        # Single date
        try:
            return [datetime.strptime(args[0], '%Y-%m-%d')]
        except ValueError as e:
            print(f"[ERROR] Invalid date format: {e}. Use YYYY-MM-DD")
            sys.exit(1)

    if len(args) == 2:
        # Date range
        try:
            start_date = datetime.strptime(args[0], '%Y-%m-%d')
            end_date = datetime.strptime(args[1], '%Y-%m-%d')

            if start_date > end_date:
                print("[ERROR] Start date cannot be after end date")
                sys.exit(1)

            dates = []
            current_date = start_date
            while current_date <= end_date:
                dates.append(current_date)
                current_date += timedelta(days=1)

            return dates

        except ValueError as e:
            print(f"[ERROR] Invalid date format: {e}. Use YYYY-MM-DD")
            sys.exit(1)

    print("[ERROR] Invalid arguments. Usage:")
    print("  python tngd_backup_improved.py                    # Today")
    print("  python tngd_backup_improved.py 2025-03-01         # Single date")
    print("  python tngd_backup_improved.py 2025-03-01 2025-03-31  # Date range")
    sys.exit(1)


def main():
    """Main function for improved backup system."""
    try:
        # Parse command line arguments
        dates = parse_dates(sys.argv[1:])

        # Create improved backup system
        backup = ImprovedTngdBackup(dates)

        # Load tables
        tables = backup.load_tables()

        # Run improved backup
        results = backup.run_improved_backup(dates, tables)

        # Exit with appropriate code
        if results.get('status') == 'completed':
            print("[OK] Backup completed successfully!")
            sys.exit(0)
        else:
            print("[ERROR] Backup failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n[ERROR] Backup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
