#!/usr/bin/env python3
"""
TNGD Backup System - Improved Version
=====================================

Enhanced backup system with better resource management, thread control,
and error handling to prevent incomplete backups.

Key Improvements:
- Thread pool management to prevent "can't start new thread" errors
- Memory monitoring and throttling
- Better error recovery and retry logic
- Resource cleanup and garbage collection
- Progress checkpointing for resume capability

Usage:
    python tngd_backup_improved.py                    # Today's data
    python tngd_backup_improved.py 2025-03-01         # Single date
    python tngd_backup_improved.py 2025-03-01 2025-03-31  # Date range
"""

import os
import sys
import time
import json
import logging
import tempfile
import threading
import gc
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config_manager import ConfigManager
from core.devo_client import DevoClient
from core.storage_manager import StorageManager
from core.email_service import EmailService
from core.streaming_processor import StreamingDataProcessor, StreamingConfig
from core.thread_manager import get_thread_manager, managed_thread_pool, log_thread_metrics


@dataclass
class BackupCheckpoint:
    """Checkpoint data for backup resume capability."""
    backup_id: str
    start_time: datetime
    dates: List[str]
    tables: List[str]
    completed_tables: Dict[str, List[str]]  # date -> [table_names]
    failed_tables: Dict[str, List[str]]     # date -> [table_names]
    current_date: Optional[str] = None
    current_table: Optional[str] = None


class ImprovedTngdBackup:
    """Improved TNGD backup system with enhanced resource management."""
    
    def __init__(self, dates: Optional[List[datetime]] = None):
        """Initialize the improved backup system."""
        self.dates = dates or []
        self.backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Setup logging
        self.log_file_path = self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize checkpoint system
        self.checkpoint_file = f"checkpoints/{self.backup_id}.json"
        os.makedirs("checkpoints", exist_ok=True)
        
        # Initialize clients
        self.config_manager = ConfigManager()
        self.devo_client = None
        self.storage_manager = None
        self.email_service = None
        self.streaming_processor = None
        
        # Resource management settings
        self.MAX_RETRIES = 3
        self.RETRY_DELAY = 60
        self.QUERY_TIMEOUT = 1800  # 30 minutes
        self.LARGE_TABLE_TIMEOUT = 3600  # 1 hour
        self.MEMORY_THRESHOLD_MB = 2000  # 2GB memory threshold
        self.THREAD_LIMIT = 6  # Conservative thread limit
        
        # Resource monitoring
        self.thread_manager = get_thread_manager()
        self.resource_check_interval = 300  # 5 minutes
        self.last_resource_check = 0
        
        # Checkpoint data
        self.checkpoint = BackupCheckpoint(
            backup_id=self.backup_id,
            start_time=datetime.now(),
            dates=[d.strftime('%Y-%m-%d') for d in self.dates],
            tables=[],
            completed_tables={},
            failed_tables={}
        )
        
        self.logger.info(f"ImprovedTngdBackup initialized: {self.backup_id}")
    
    def setup_logging(self):
        """Setup enhanced logging with resource monitoring."""
        os.makedirs('logs', exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        date_info = ""
        if self.dates:
            if len(self.dates) == 1:
                date_info = f"_{self.dates[0].strftime('%Y-%m-%d')}"
            else:
                start_date = self.dates[0].strftime('%Y-%m-%d')
                end_date = self.dates[-1].strftime('%Y-%m-%d')
                date_info = f"_{start_date}_to_{end_date}"
        
        log_filename = f"tngd_backup_improved_{timestamp}{date_info}.log"
        log_file_path = os.path.join('logs', log_filename)
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file_path, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return log_file_path
    
    def save_checkpoint(self):
        """Save current backup progress to checkpoint file."""
        try:
            with open(self.checkpoint_file, 'w') as f:
                # Convert datetime objects to strings for JSON serialization
                checkpoint_data = asdict(self.checkpoint)
                checkpoint_data['start_time'] = self.checkpoint.start_time.isoformat()
                json.dump(checkpoint_data, f, indent=2)
            
            self.logger.debug(f"Checkpoint saved: {self.checkpoint_file}")
        except Exception as e:
            self.logger.error(f"Failed to save checkpoint: {e}")
    
    def load_checkpoint(self, checkpoint_file: str) -> Optional[BackupCheckpoint]:
        """Load backup checkpoint for resume capability."""
        try:
            if not os.path.exists(checkpoint_file):
                return None
            
            with open(checkpoint_file, 'r') as f:
                data = json.load(f)
            
            # Convert string back to datetime
            data['start_time'] = datetime.fromisoformat(data['start_time'])
            
            return BackupCheckpoint(**data)
        except Exception as e:
            self.logger.error(f"Failed to load checkpoint: {e}")
            return None
    
    def check_resources(self):
        """Check system resources and apply throttling if needed."""
        current_time = time.time()
        if current_time - self.last_resource_check < self.resource_check_interval:
            return
        
        self.last_resource_check = current_time
        
        try:
            # Log thread metrics
            log_thread_metrics()
            
            # Get resource metrics
            metrics = self.thread_manager.get_metrics()
            
            # Check memory usage
            if metrics.memory_usage_mb > self.MEMORY_THRESHOLD_MB:
                self.logger.warning(f"High memory usage: {metrics.memory_usage_mb:.1f}MB")
                
                # Force garbage collection
                gc.collect()
                
                # Apply throttling
                time.sleep(5)
            
            # Check thread count
            if metrics.active_threads > self.THREAD_LIMIT * 2:
                self.logger.warning(f"High thread count: {metrics.active_threads}")
                
                # Force cleanup
                self.thread_manager._force_cleanup()
                time.sleep(2)
        
        except Exception as e:
            self.logger.error(f"Resource check failed: {e}")
    
    def initialize_clients(self):
        """Initialize all client connections with improved error handling."""
        self.log_step("INIT", "Initializing clients...")
        
        try:
            # Initialize Devo client
            self.devo_client = DevoClient()
            self.log_step("INIT", "✅ Devo client initialized")
            
            # Initialize storage manager
            self.storage_manager = StorageManager(self.config_manager)
            self.log_step("INIT", "✅ Storage manager initialized")
            
            # Initialize email service
            self.email_service = EmailService(self.config_manager)
            self.log_step("INIT", "✅ Email service initialized")
            
            # Initialize streaming processor
            streaming_config = StreamingConfig(
                default_chunk_size=50000,  # Smaller chunks for better memory management
                streaming_threshold_rows=100000,  # Lower threshold
                memory_threshold_mb=self.MEMORY_THRESHOLD_MB
            )
            self.streaming_processor = StreamingDataProcessor(streaming_config, self.log_step)
            self.log_step("INIT", "✅ Streaming processor initialized")
            
            return True
            
        except Exception as e:
            self.log_step("INIT", f"❌ Client initialization failed: {e}", "ERROR")
            return False
    
    def log_step(self, step: str, message: str, level: str = "INFO"):
        """Enhanced logging with resource monitoring."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {step}: {message}"
        
        if level == "ERROR":
            self.logger.error(formatted_message)
        elif level == "WARNING":
            self.logger.warning(formatted_message)
        else:
            self.logger.info(formatted_message)
        
        # Periodic resource checks
        if step in ["TABLE_START", "TABLE_END", "DATE_START", "DATE_END"]:
            self.check_resources()
    
    def load_tables(self) -> List[str]:
        """Load table list with validation."""
        try:
            tables = self.config_manager.load_tables()
            self.checkpoint.tables = tables
            self.save_checkpoint()
            
            self.log_step("CONFIG", f"✅ Loaded {len(tables)} tables")
            return tables
            
        except Exception as e:
            self.log_step("CONFIG", f"❌ Failed to load tables: {e}", "ERROR")
            raise

    def backup_single_table_improved(self, table_name: str, target_date: datetime) -> Dict[str, Any]:
        """Improved single table backup with better resource management."""
        date_str = target_date.strftime('%Y-%m-%d')

        # Update checkpoint
        self.checkpoint.current_date = date_str
        self.checkpoint.current_table = table_name
        self.save_checkpoint()

        self.log_step("TABLE_START", f"Starting backup: {table_name} for {date_str}")

        result = {
            'table_name': table_name,
            'date': date_str,
            'status': 'failed',
            'rows': 0,
            'error': None,
            'duration': 0,
            'retry_count': 0
        }

        start_time = time.time()

        try:
            # Use managed thread pool for this table
            with managed_thread_pool(f"table-{table_name}", max_workers=2) as pool:

                # Step 1: Query data with improved error handling
                self.log_step("STEP_1", f"Querying data from Devo API...")

                for attempt in range(self.MAX_RETRIES + 1):
                    try:
                        result['retry_count'] = attempt

                        # Check resources before each attempt
                        self.check_resources()

                        # Query data
                        data = self.query_table_data_improved(table_name, target_date)

                        if not data:
                            result['status'] = 'no_data'
                            self.log_step("NO_DATA", f"No data found for {table_name} on {date_str}")
                            break

                        result['rows'] = len(data)
                        self.log_step("QUERY", f"✅ Retrieved {result['rows']:,} rows")

                        # Step 2: Save to temp file
                        self.log_step("STEP_2", f"Saving {result['rows']:,} rows to temporary file...")
                        temp_file = self.save_to_temp_file_improved(data, table_name, target_date)

                        # Step 3: Upload to OSS
                        self.log_step("STEP_3", f"Uploading to OSS...")
                        upload_success = self.upload_to_oss_improved(temp_file, table_name, target_date)

                        if upload_success:
                            result['status'] = 'completed'
                            self.log_step("TABLE_SUCCESS", f"✅ {table_name} backup completed successfully")

                            # Update checkpoint
                            if date_str not in self.checkpoint.completed_tables:
                                self.checkpoint.completed_tables[date_str] = []
                            self.checkpoint.completed_tables[date_str].append(table_name)

                        else:
                            result['status'] = 'upload_failed'
                            result['error'] = 'Upload to OSS failed'
                            self.log_step("TABLE_FAILED", f"❌ {table_name} backup failed - upload error")

                        # Clean up temp file
                        self.cleanup_temp_file(temp_file)
                        break

                    except Exception as e:
                        error_msg = str(e)
                        result['error'] = error_msg

                        if attempt < self.MAX_RETRIES:
                            self.log_step("RETRY", f"❌ Attempt {attempt + 1} failed: {error_msg}", "WARNING")
                            self.log_step("RETRY", f"Waiting {self.RETRY_DELAY} seconds before retry...")

                            # Force resource cleanup before retry
                            gc.collect()
                            time.sleep(self.RETRY_DELAY)
                        else:
                            self.log_step("TABLE_FAILED", f"❌ {table_name} backup failed after {self.MAX_RETRIES + 1} attempts: {error_msg}", "ERROR")

                            # Update checkpoint with failed table
                            if date_str not in self.checkpoint.failed_tables:
                                self.checkpoint.failed_tables[date_str] = []
                            self.checkpoint.failed_tables[date_str].append(table_name)

        except Exception as e:
            result['error'] = str(e)
            self.log_step("TABLE_FAILED", f"❌ {table_name} backup failed: {str(e)}", "ERROR")

        finally:
            result['duration'] = time.time() - start_time
            self.log_step("TABLE_END", f"Table {table_name} completed in {result['duration']:.1f}s")

            # Clear current table from checkpoint
            self.checkpoint.current_table = None
            self.save_checkpoint()

        return result

    def query_table_data_improved(self, table_name: str, target_date: datetime) -> List[Dict[str, Any]]:
        """Improved data querying with better resource management."""
        date_str = target_date.strftime('%Y-%m-%d')

        # Build query
        query = f"""
        from {table_name}
        where eventdate >= "{date_str} 00:00:00" and eventdate <= "{date_str} 23:59:59"
        """

        # Determine timeout based on table size
        timeout = self.LARGE_TABLE_TIMEOUT if self.is_large_table(table_name) else self.QUERY_TIMEOUT

        try:
            # Use streaming processor for large datasets
            if self.streaming_processor and self.streaming_processor.should_use_streaming(table_name, estimated_rows=0):
                self.log_step("QUERY", f"Using streaming processing for {table_name}")
                return self.query_with_streaming(query, table_name, timeout)
            else:
                self.log_step("QUERY", f"Using standard processing for {table_name}")
                return self.devo_client.execute_query(query, timeout=timeout, table_name=table_name)

        except Exception as e:
            self.log_step("QUERY", f"❌ Query failed: {str(e)}", "ERROR")
            raise

    def is_large_table(self, table_name: str) -> bool:
        """Check if table is known to be large."""
        large_tables = [
            'cef0.zscaler.nssweblog',
            'cloud.alibaba.log_service.events',
            'cloud.office365.management.exchange',
            'firewall.fortinet.traffic.forward',
            'cloud.office365.management.endpoint',
            'my.app.tngd.polardb'
        ]
        return table_name in large_tables
