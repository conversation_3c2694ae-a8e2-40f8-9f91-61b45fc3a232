#!/usr/bin/env python3
"""
Backup Maintenance and Recovery Tool

This tool provides comprehensive maintenance and recovery capabilities
for the TNGD backup system.

Features:
- Automatic recovery from incomplete backups
- System health checks and optimization
- Log analysis and error reporting
- Performance tuning recommendations
- Cleanup and maintenance tasks
"""

import os
import sys
import json
import time
import shutil
import logging
import subprocess
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class BackupMaintenance:
    """Comprehensive backup maintenance and recovery system."""
    
    def __init__(self):
        """Initialize maintenance system."""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Directories
        self.logs_dir = Path("logs")
        self.checkpoints_dir = Path("checkpoints")
        self.temp_dir = Path("temp")
        self.config_dir = Path("config")
        
        # Ensure directories exist
        for directory in [self.logs_dir, self.checkpoints_dir, self.temp_dir]:
            directory.mkdir(exist_ok=True)
        
        self.logger.info("BackupMaintenance initialized")
    
    def setup_logging(self):
        """Setup logging for maintenance tool."""
        log_file = "logs/maintenance.log"
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def analyze_latest_log(self) -> Dict[str, Any]:
        """Analyze the latest backup log for issues."""
        self.logger.info("Analyzing latest backup log...")
        
        # Find latest log file
        log_files = list(self.logs_dir.glob("tngd_backup_*.log"))
        if not log_files:
            return {"status": "no_logs", "message": "No backup logs found"}
        
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        self.logger.info(f"Analyzing log: {latest_log}")
        
        analysis = {
            "log_file": str(latest_log),
            "file_size_mb": latest_log.stat().st_size / (1024 * 1024),
            "last_modified": datetime.fromtimestamp(latest_log.stat().st_mtime),
            "errors": [],
            "warnings": [],
            "failed_tables": [],
            "completed_tables": [],
            "performance_issues": [],
            "recommendations": []
        }
        
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Analyze log content
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # Check for errors
                if "ERROR" in line:
                    analysis["errors"].append({
                        "line": line_num,
                        "message": line,
                        "type": self._classify_error(line)
                    })
                
                # Check for warnings
                elif "WARNING" in line:
                    analysis["warnings"].append({
                        "line": line_num,
                        "message": line
                    })
                
                # Check for failed tables
                elif "TABLE_FAILED" in line:
                    table_name = self._extract_table_name(line)
                    if table_name:
                        analysis["failed_tables"].append(table_name)
                
                # Check for completed tables
                elif "TABLE_SUCCESS" in line:
                    table_name = self._extract_table_name(line)
                    if table_name:
                        analysis["completed_tables"].append(table_name)
                
                # Check for performance issues
                elif any(issue in line for issue in ["can't start new thread", "malloc failure", "timeout", "hanging"]):
                    analysis["performance_issues"].append({
                        "line": line_num,
                        "issue": line
                    })
            
            # Generate recommendations
            analysis["recommendations"] = self._generate_recommendations(analysis)
            
            # Summary
            analysis["summary"] = {
                "total_errors": len(analysis["errors"]),
                "total_warnings": len(analysis["warnings"]),
                "failed_tables_count": len(analysis["failed_tables"]),
                "completed_tables_count": len(analysis["completed_tables"]),
                "performance_issues_count": len(analysis["performance_issues"]),
                "log_complete": not any("can't start new thread" in error["message"] for error in analysis["errors"])
            }
        
        except Exception as e:
            self.logger.error(f"Error analyzing log: {e}")
            analysis["error"] = str(e)
        
        return analysis
    
    def _classify_error(self, error_line: str) -> str:
        """Classify error type."""
        if "can't start new thread" in error_line:
            return "threading"
        elif "malloc failure" in error_line:
            return "memory"
        elif "Response ended prematurely" in error_line:
            return "network"
        elif "Failed to establish" in error_line:
            return "connection"
        elif "timeout" in error_line.lower():
            return "timeout"
        else:
            return "other"
    
    def _extract_table_name(self, log_line: str) -> Optional[str]:
        """Extract table name from log line."""
        try:
            # Look for patterns like "TABLE_FAILED: ❌ table_name backup failed"
            if "backup failed" in log_line:
                parts = log_line.split()
                for i, part in enumerate(parts):
                    if "backup" in part and i > 0:
                        return parts[i-1]
            return None
        except:
            return None
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # Threading issues
        threading_errors = [e for e in analysis["errors"] if e["type"] == "threading"]
        if threading_errors:
            recommendations.append("🔧 CRITICAL: Reduce max_threads in configuration (suggested: 4-6)")
            recommendations.append("🔧 Implement thread pool management with proper cleanup")
            recommendations.append("🔧 Add resource monitoring and throttling")
        
        # Memory issues
        memory_errors = [e for e in analysis["errors"] if e["type"] == "memory"]
        if memory_errors:
            recommendations.append("🔧 CRITICAL: Reduce chunk sizes for streaming (suggested: 25000-50000)")
            recommendations.append("🔧 Lower memory thresholds (suggested: 1500-2000MB)")
            recommendations.append("🔧 Implement aggressive garbage collection")
        
        # Network issues
        network_errors = [e for e in analysis["errors"] if e["type"] == "network"]
        if network_errors:
            recommendations.append("🔧 Increase connection timeouts")
            recommendations.append("🔧 Implement exponential backoff for retries")
            recommendations.append("🔧 Add connection pooling and keep-alive")
        
        # Performance issues
        if analysis["performance_issues"]:
            recommendations.append("🔧 Enable resource monitoring during backup")
            recommendations.append("🔧 Implement adaptive throttling based on system load")
            recommendations.append("🔧 Consider running backups during off-peak hours")
        
        # Failed tables
        if analysis["failed_tables"]:
            recommendations.append(f"🔧 Retry failed tables: {', '.join(analysis['failed_tables'][:5])}")
            recommendations.append("🔧 Consider table-specific timeout and retry settings")
        
        # General recommendations
        if not recommendations:
            recommendations.append("✅ No critical issues found - system appears healthy")
        
        return recommendations
    
    def cleanup_system(self) -> Dict[str, Any]:
        """Perform comprehensive system cleanup."""
        self.logger.info("Starting system cleanup...")
        
        cleanup_results = {
            "temp_files_removed": 0,
            "temp_size_freed_mb": 0,
            "old_logs_removed": 0,
            "log_size_freed_mb": 0,
            "checkpoints_cleaned": 0,
            "errors": []
        }
        
        try:
            # Clean temp files
            if self.temp_dir.exists():
                temp_size_before = sum(f.stat().st_size for f in self.temp_dir.rglob('*') if f.is_file())
                
                for temp_file in self.temp_dir.rglob('*'):
                    if temp_file.is_file():
                        try:
                            temp_file.unlink()
                            cleanup_results["temp_files_removed"] += 1
                        except Exception as e:
                            cleanup_results["errors"].append(f"Failed to remove {temp_file}: {e}")
                
                temp_size_after = sum(f.stat().st_size for f in self.temp_dir.rglob('*') if f.is_file())
                cleanup_results["temp_size_freed_mb"] = (temp_size_before - temp_size_after) / (1024 * 1024)
            
            # Clean old logs (keep last 10)
            log_files = sorted(self.logs_dir.glob("tngd_backup_*.log"), key=lambda f: f.stat().st_mtime)
            if len(log_files) > 10:
                old_logs = log_files[:-10]
                for log_file in old_logs:
                    try:
                        log_size = log_file.stat().st_size
                        log_file.unlink()
                        cleanup_results["old_logs_removed"] += 1
                        cleanup_results["log_size_freed_mb"] += log_size / (1024 * 1024)
                    except Exception as e:
                        cleanup_results["errors"].append(f"Failed to remove {log_file}: {e}")
            
            # Clean old checkpoints (older than 7 days)
            cutoff_date = datetime.now() - timedelta(days=7)
            for checkpoint_file in self.checkpoints_dir.glob("*.json"):
                try:
                    if datetime.fromtimestamp(checkpoint_file.stat().st_mtime) < cutoff_date:
                        checkpoint_file.unlink()
                        cleanup_results["checkpoints_cleaned"] += 1
                except Exception as e:
                    cleanup_results["errors"].append(f"Failed to remove {checkpoint_file}: {e}")
        
        except Exception as e:
            cleanup_results["errors"].append(f"General cleanup error: {e}")
        
        self.logger.info(f"Cleanup completed: {cleanup_results}")
        return cleanup_results
    
    def check_system_health(self) -> Dict[str, Any]:
        """Perform comprehensive system health check."""
        self.logger.info("Performing system health check...")
        
        health = {
            "timestamp": datetime.now().isoformat(),
            "disk_space": {},
            "memory": {},
            "processes": {},
            "configuration": {},
            "recommendations": []
        }
        
        try:
            import psutil
            
            # Disk space
            disk_usage = psutil.disk_usage('.')
            health["disk_space"] = {
                "total_gb": disk_usage.total / (1024**3),
                "used_gb": disk_usage.used / (1024**3),
                "free_gb": disk_usage.free / (1024**3),
                "percent_used": (disk_usage.used / disk_usage.total) * 100
            }
            
            # Memory
            memory = psutil.virtual_memory()
            health["memory"] = {
                "total_gb": memory.total / (1024**3),
                "available_gb": memory.available / (1024**3),
                "percent_used": memory.percent
            }
            
            # Processes
            health["processes"] = {
                "total_processes": len(psutil.pids()),
                "python_processes": len([p for p in psutil.process_iter(['name']) if 'python' in p.info['name'].lower()])
            }
            
            # Configuration check
            config_file = self.config_dir / "config.json"
            if config_file.exists():
                health["configuration"]["config_exists"] = True
                health["configuration"]["config_size_kb"] = config_file.stat().st_size / 1024
            else:
                health["configuration"]["config_exists"] = False
                health["recommendations"].append("⚠️ Main configuration file not found")
            
            # Health recommendations
            if health["disk_space"]["percent_used"] > 85:
                health["recommendations"].append("⚠️ Disk space is running low (>85% used)")
            
            if health["memory"]["percent_used"] > 80:
                health["recommendations"].append("⚠️ Memory usage is high (>80% used)")
            
            if not health["recommendations"]:
                health["recommendations"].append("✅ System health looks good")
        
        except ImportError:
            health["error"] = "psutil not available - install with: pip install psutil"
        except Exception as e:
            health["error"] = str(e)
        
        return health
    
    def generate_maintenance_report(self) -> str:
        """Generate comprehensive maintenance report."""
        self.logger.info("Generating maintenance report...")
        
        # Collect all information
        log_analysis = self.analyze_latest_log()
        system_health = self.check_system_health()
        
        # Build report
        report_lines = [
            "=" * 60,
            "TNGD BACKUP SYSTEM - MAINTENANCE REPORT",
            "=" * 60,
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "📊 LATEST BACKUP ANALYSIS",
            "-" * 30
        ]
        
        if "summary" in log_analysis:
            summary = log_analysis["summary"]
            report_lines.extend([
                f"Log File: {log_analysis.get('log_file', 'N/A')}",
                f"File Size: {log_analysis.get('file_size_mb', 0):.1f} MB",
                f"Errors: {summary['total_errors']}",
                f"Warnings: {summary['total_warnings']}",
                f"Failed Tables: {summary['failed_tables_count']}",
                f"Completed Tables: {summary['completed_tables_count']}",
                f"Performance Issues: {summary['performance_issues_count']}",
                f"Backup Complete: {'✅ Yes' if summary['log_complete'] else '❌ No'}",
                ""
            ])
        
        # Add recommendations
        if "recommendations" in log_analysis and log_analysis["recommendations"]:
            report_lines.extend([
                "🔧 RECOMMENDATIONS",
                "-" * 20
            ])
            report_lines.extend(log_analysis["recommendations"])
            report_lines.append("")
        
        # Add system health
        report_lines.extend([
            "🏥 SYSTEM HEALTH",
            "-" * 15
        ])
        
        if "disk_space" in system_health:
            disk = system_health["disk_space"]
            report_lines.append(f"Disk Usage: {disk['percent_used']:.1f}% ({disk['free_gb']:.1f}GB free)")
        
        if "memory" in system_health:
            memory = system_health["memory"]
            report_lines.append(f"Memory Usage: {memory['percent_used']:.1f}% ({memory['available_gb']:.1f}GB available)")
        
        if "recommendations" in system_health:
            report_lines.extend([""] + system_health["recommendations"])
        
        report_lines.extend([
            "",
            "=" * 60,
            "End of Report"
        ])
        
        return "\n".join(report_lines)


def main():
    """Main function for maintenance tool."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Backup Maintenance and Recovery Tool")
    parser.add_argument("--analyze", action="store_true", help="Analyze latest backup log")
    parser.add_argument("--cleanup", action="store_true", help="Perform system cleanup")
    parser.add_argument("--health", action="store_true", help="Check system health")
    parser.add_argument("--report", action="store_true", help="Generate maintenance report")
    parser.add_argument("--all", action="store_true", help="Run all maintenance tasks")
    
    args = parser.parse_args()
    
    maintenance = BackupMaintenance()
    
    if args.all or args.analyze:
        analysis = maintenance.analyze_latest_log()
        print(f"Log Analysis: {analysis['summary'] if 'summary' in analysis else 'Failed'}")
    
    if args.all or args.cleanup:
        cleanup_result = maintenance.cleanup_system()
        print(f"Cleanup: Removed {cleanup_result['temp_files_removed']} temp files, "
              f"freed {cleanup_result['temp_size_freed_mb']:.1f}MB")
    
    if args.all or args.health:
        health = maintenance.check_system_health()
        if "disk_space" in health:
            print(f"Health: Disk {health['disk_space']['percent_used']:.1f}% used, "
                  f"Memory {health['memory']['percent_used']:.1f}% used")
    
    if args.all or args.report:
        report = maintenance.generate_maintenance_report()
        print(report)
        
        # Save report to file
        report_file = f"logs/maintenance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w') as f:
            f.write(report)
        print(f"\nReport saved to: {report_file}")
    
    if not any([args.analyze, args.cleanup, args.health, args.report, args.all]):
        parser.print_help()


if __name__ == "__main__":
    main()
