{"version": "2.0-emergency", "description": "Emergency configuration for backup recovery", "resource_management": {"max_threads": 4, "memory_threshold_mb": 1500, "cpu_threshold_percent": 75, "cleanup_interval_seconds": 180, "resource_check_interval_seconds": 30}, "query_settings": {"default_timeout_seconds": 1200, "large_table_timeout_seconds": 2400, "max_retries": 2, "retry_delay_seconds": 30, "connection_timeout_seconds": 20, "read_timeout_seconds": 180}, "streaming_config": {"enabled": true, "default_chunk_size": 20000, "max_chunk_size": 50000, "min_chunk_size": 5000, "streaming_threshold_rows": 50000, "memory_threshold_mb": 1000, "progress_report_interval": 3, "memory_check_interval": 2, "enable_adaptive_chunking": true, "chunk_size_adjustment_factor": 0.7, "temp_file_cleanup": true}, "large_tables": ["cef0.zscaler.nssweblog", "cloud.alibaba.log_service.events", "firewall.fortinet.traffic.forward", "cloud.office365.management.endpoint", "my.app.tngd.polardb"], "table_specific_settings": {"cef0.zscaler.nssweblog": {"chunk_size": 10000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 500}, "firewall.fortinet.traffic.forward": {"chunk_size": 15000, "timeout_seconds": 2400, "max_retries": 3, "memory_limit_mb": 600}, "cloud.office365.management.endpoint": {"chunk_size": 15000, "timeout_seconds": 2400, "max_retries": 3, "memory_limit_mb": 600}}, "error_handling": {"max_consecutive_failures": 3, "failure_cooldown_minutes": 5, "auto_skip_problematic_tables": true, "detailed_error_logging": true}, "monitoring": {"enabled": true, "health_check_interval_seconds": 60, "alert_thresholds": {"cpu_warning": 70, "cpu_critical": 85, "memory_warning": 65, "memory_critical": 80, "thread_warning": 50, "thread_critical": 100}}}