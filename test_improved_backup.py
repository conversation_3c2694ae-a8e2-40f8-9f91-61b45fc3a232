#!/usr/bin/env python3
"""
Test Script for Improved TNGD Backup System

This script tests the improved backup system with a single table
to verify all components are working correctly before running
the full backup.
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tngd_backup_improved import ImprovedTngdBackup


def test_system_components():
    """Test individual system components."""
    print("🧪 Testing System Components...")
    
    # Test 1: Thread Manager
    try:
        from core.thread_manager import get_thread_manager, log_thread_metrics
        manager = get_thread_manager()
        metrics = manager.get_metrics()
        print(f"[OK] Thread Manager: Active threads = {metrics.active_threads}")
    except Exception as e:
        print(f"[ERROR] Thread Manager failed: {e}")
        return False
    
    # Test 2: Configuration
    try:
        from core.config_manager import ConfigManager
        config = ConfigManager()
        print("[OK] Configuration Manager: Loaded successfully")
    except Exception as e:
        print(f"[ERROR] Configuration Manager failed: {e}")
        return False
    
    # Test 3: Resource Monitor
    try:
        from tools.resource_monitor import ResourceMonitor
        monitor = ResourceMonitor()
        health = monitor.get_system_health()
        print(f"[OK] Resource Monitor: Status = {health.status}")
    except Exception as e:
        print(f"[ERROR] Resource Monitor failed: {e}")
        return False
    
    return True


def test_single_table_backup():
    """Test backup with a single small table."""
    print("\n🧪 Testing Single Table Backup...")
    
    try:
        # Use yesterday's date for testing
        test_date = datetime.now() - timedelta(days=1)
        
        # Create backup instance
        backup = ImprovedTngdBackup([test_date])
        
        # Initialize clients
        if not backup.initialize_clients():
            print("[ERROR] Failed to initialize clients")
            return False
        
        # Load tables
        tables = backup.load_tables()
        if not tables:
            print("[ERROR] No tables loaded")
            return False
        
        # Test with first table only
        test_table = tables[0]
        print(f"Testing with table: {test_table}")
        
        # Test single table backup
        result = backup.backup_single_table_improved(test_table, test_date)
        
        if result['status'] in ['completed', 'no_data']:
            print(f"[OK] Single table backup successful: {result['status']}")
            print(f"   Rows: {result['rows']:,}")
            print(f"   Duration: {result['duration']:.1f}s")
            return True
        else:
            print(f"[ERROR] Single table backup failed: {result.get('error', 'Unknown error')}")
            return False
    
    except Exception as e:
        print(f"[ERROR] Single table backup test failed: {e}")
        return False


def test_resource_monitoring():
    """Test resource monitoring during backup."""
    print("\n🧪 Testing Resource Monitoring...")
    
    try:
        from tools.resource_monitor import ResourceMonitor
        
        monitor = ResourceMonitor()
        
        # Start monitoring
        monitor.start_monitoring(interval=5)
        
        # Wait a bit
        import time
        time.sleep(10)
        
        # Get health summary
        summary = monitor.get_health_summary()
        print(f"[OK] Resource monitoring working:")
        print(f"   Current CPU: {summary['current_cpu']:.1f}%")
        print(f"   Current Memory: {summary['current_memory']:.1f}%")
        print(f"   Current Threads: {summary['current_threads']}")
        
        # Stop monitoring
        monitor.stop_monitoring()
        
        return True
    
    except Exception as e:
        print(f"[ERROR] Resource monitoring test failed: {e}")
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("IMPROVED TNGD BACKUP SYSTEM - COMPONENT TESTS")
    print("=" * 60)
    
    # Test 1: System Components
    if not test_system_components():
        print("\n[ERROR] System component tests failed!")
        return False
    
    # Test 2: Resource Monitoring
    if not test_resource_monitoring():
        print("\n[ERROR] Resource monitoring tests failed!")
        return False
    
    # Test 3: Single Table Backup
    if not test_single_table_backup():
        print("\n[ERROR] Single table backup test failed!")
        return False
    
    print("\n" + "=" * 60)
    print("[OK] ALL TESTS PASSED - SYSTEM READY FOR FULL BACKUP")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Run full backup: python tngd_backup_improved.py 2025-03-26 2025-03-31")
    print("2. Monitor progress: python tools/resource_monitor.py --monitor")
    print("3. Check logs in logs/ directory")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n[ERROR] Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n[ERROR] Unexpected test error: {e}")
        sys.exit(1)
