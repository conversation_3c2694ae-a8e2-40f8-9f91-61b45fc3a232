#!/usr/bin/env python3
"""
Resource Monitor and Recovery Tool

This tool monitors system resources during backup operations and provides
recovery mechanisms for incomplete backups.

Features:
- Real-time resource monitoring
- Automatic recovery from checkpoints
- Memory and thread cleanup
- Backup health checks
"""

import os
import sys
import json
import time
import psutil
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


@dataclass
class SystemHealth:
    """System health metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_percent: float
    thread_count: int
    process_count: int
    network_connections: int
    status: str  # healthy, warning, critical


class ResourceMonitor:
    """System resource monitor with recovery capabilities."""
    
    def __init__(self, log_file: str = "logs/resource_monitor.log"):
        """Initialize resource monitor."""
        self.log_file = log_file
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Thresholds
        self.cpu_warning = 80.0
        self.cpu_critical = 95.0
        self.memory_warning = 75.0
        self.memory_critical = 90.0
        self.thread_warning = 100
        self.thread_critical = 200
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread = None
        self.health_history: List[SystemHealth] = []
        self.max_history = 1000  # Keep last 1000 readings
        
        self.logger.info("ResourceMonitor initialized")
    
    def setup_logging(self):
        """Setup logging for resource monitor."""
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
    
    def get_system_health(self) -> SystemHealth:
        """Get current system health metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_mb = memory.used / (1024 * 1024)
            
            # Disk usage
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            
            # Process and thread counts
            process_count = len(psutil.pids())
            thread_count = sum(p.num_threads() for p in psutil.process_iter(['num_threads']) 
                             if p.info['num_threads'] is not None)
            
            # Network connections
            network_connections = len(psutil.net_connections())
            
            # Determine status
            status = self._determine_status(cpu_percent, memory_percent, thread_count)
            
            return SystemHealth(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_mb=memory_mb,
                disk_percent=disk_percent,
                thread_count=thread_count,
                process_count=process_count,
                network_connections=network_connections,
                status=status
            )
        
        except Exception as e:
            self.logger.error(f"Failed to get system health: {e}")
            return SystemHealth(
                timestamp=datetime.now(),
                cpu_percent=0, memory_percent=0, memory_mb=0,
                disk_percent=0, thread_count=0, process_count=0,
                network_connections=0, status="error"
            )
    
    def _determine_status(self, cpu: float, memory: float, threads: int) -> str:
        """Determine system status based on metrics."""
        if (cpu >= self.cpu_critical or 
            memory >= self.memory_critical or 
            threads >= self.thread_critical):
            return "critical"
        elif (cpu >= self.cpu_warning or 
              memory >= self.memory_warning or 
              threads >= self.thread_warning):
            return "warning"
        else:
            return "healthy"
    
    def start_monitoring(self, interval: int = 30):
        """Start continuous monitoring."""
        if self.monitoring:
            self.logger.warning("Monitoring already started")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            name="resource-monitor",
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info(f"Started resource monitoring (interval: {interval}s)")
    
    def stop_monitoring(self):
        """Stop monitoring."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Stopped resource monitoring")
    
    def _monitor_loop(self, interval: int):
        """Main monitoring loop."""
        while self.monitoring:
            try:
                health = self.get_system_health()
                self.health_history.append(health)
                
                # Trim history
                if len(self.health_history) > self.max_history:
                    self.health_history = self.health_history[-self.max_history:]
                
                # Log status changes
                if len(self.health_history) > 1:
                    prev_status = self.health_history[-2].status
                    if health.status != prev_status:
                        self.logger.warning(f"System status changed: {prev_status} -> {health.status}")
                
                # Log critical conditions
                if health.status == "critical":
                    self.logger.error(f"CRITICAL: CPU={health.cpu_percent:.1f}%, "
                                    f"Memory={health.memory_percent:.1f}%, "
                                    f"Threads={health.thread_count}")
                elif health.status == "warning":
                    self.logger.warning(f"WARNING: CPU={health.cpu_percent:.1f}%, "
                                      f"Memory={health.memory_percent:.1f}%, "
                                      f"Threads={health.thread_count}")
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"Monitor loop error: {e}")
                time.sleep(interval)
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary."""
        if not self.health_history:
            return {"status": "no_data", "message": "No health data available"}
        
        recent = self.health_history[-10:]  # Last 10 readings
        current = self.health_history[-1]
        
        avg_cpu = sum(h.cpu_percent for h in recent) / len(recent)
        avg_memory = sum(h.memory_percent for h in recent) / len(recent)
        max_threads = max(h.thread_count for h in recent)
        
        return {
            "current_status": current.status,
            "current_cpu": current.cpu_percent,
            "current_memory": current.memory_percent,
            "current_threads": current.thread_count,
            "avg_cpu_10min": avg_cpu,
            "avg_memory_10min": avg_memory,
            "max_threads_10min": max_threads,
            "total_readings": len(self.health_history),
            "monitoring_duration": (current.timestamp - self.health_history[0].timestamp).total_seconds() / 60
        }
    
    def cleanup_system_resources(self):
        """Perform system resource cleanup."""
        self.logger.info("Starting system resource cleanup...")
        
        try:
            # Force garbage collection
            import gc
            gc.collect()
            
            # Clear Python caches
            sys.modules.clear()
            
            # Log cleanup results
            health_before = self.get_system_health()
            time.sleep(2)
            health_after = self.get_system_health()
            
            memory_freed = health_before.memory_mb - health_after.memory_mb
            threads_freed = health_before.thread_count - health_after.thread_count
            
            self.logger.info(f"Cleanup completed: "
                           f"Memory freed: {memory_freed:.1f}MB, "
                           f"Threads freed: {threads_freed}")
            
            return {
                "memory_freed_mb": memory_freed,
                "threads_freed": threads_freed,
                "success": True
            }
        
        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")
            return {"success": False, "error": str(e)}


class BackupRecovery:
    """Backup recovery and checkpoint management."""
    
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        """Initialize backup recovery."""
        self.checkpoint_dir = checkpoint_dir
        self.logger = logging.getLogger(f"{__name__}.BackupRecovery")
        
        os.makedirs(checkpoint_dir, exist_ok=True)
    
    def find_incomplete_backups(self) -> List[Dict[str, Any]]:
        """Find incomplete backup checkpoints."""
        incomplete = []
        
        try:
            for filename in os.listdir(self.checkpoint_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.checkpoint_dir, filename)
                    
                    try:
                        with open(filepath, 'r') as f:
                            checkpoint = json.load(f)
                        
                        # Check if backup is incomplete
                        if checkpoint.get('current_table') or checkpoint.get('current_date'):
                            incomplete.append({
                                'file': filename,
                                'backup_id': checkpoint.get('backup_id'),
                                'start_time': checkpoint.get('start_time'),
                                'current_date': checkpoint.get('current_date'),
                                'current_table': checkpoint.get('current_table'),
                                'completed_tables': len(checkpoint.get('completed_tables', {})),
                                'failed_tables': len(checkpoint.get('failed_tables', {}))
                            })
                    
                    except Exception as e:
                        self.logger.error(f"Error reading checkpoint {filename}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error scanning checkpoints: {e}")
        
        return incomplete
    
    def generate_recovery_report(self) -> str:
        """Generate recovery report."""
        incomplete = self.find_incomplete_backups()
        
        report = ["=== BACKUP RECOVERY REPORT ===", ""]
        
        if not incomplete:
            report.append("✅ No incomplete backups found")
        else:
            report.append(f"❌ Found {len(incomplete)} incomplete backups:")
            report.append("")
            
            for backup in incomplete:
                report.append(f"Backup ID: {backup['backup_id']}")
                report.append(f"  Started: {backup['start_time']}")
                report.append(f"  Current Date: {backup['current_date']}")
                report.append(f"  Current Table: {backup['current_table']}")
                report.append(f"  Completed Tables: {backup['completed_tables']}")
                report.append(f"  Failed Tables: {backup['failed_tables']}")
                report.append("")
        
        return "\n".join(report)


def main():
    """Main function for resource monitoring tool."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Resource Monitor and Recovery Tool")
    parser.add_argument("--monitor", action="store_true", help="Start resource monitoring")
    parser.add_argument("--cleanup", action="store_true", help="Perform system cleanup")
    parser.add_argument("--recovery", action="store_true", help="Show recovery report")
    parser.add_argument("--interval", type=int, default=30, help="Monitoring interval in seconds")
    
    args = parser.parse_args()
    
    if args.monitor:
        monitor = ResourceMonitor()
        monitor.start_monitoring(args.interval)
        
        try:
            while True:
                time.sleep(60)
                summary = monitor.get_health_summary()
                print(f"Status: {summary['current_status']}, "
                      f"CPU: {summary['current_cpu']:.1f}%, "
                      f"Memory: {summary['current_memory']:.1f}%, "
                      f"Threads: {summary['current_threads']}")
        except KeyboardInterrupt:
            monitor.stop_monitoring()
    
    elif args.cleanup:
        monitor = ResourceMonitor()
        result = monitor.cleanup_system_resources()
        print(f"Cleanup result: {result}")
    
    elif args.recovery:
        recovery = BackupRecovery()
        report = recovery.generate_recovery_report()
        print(report)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
